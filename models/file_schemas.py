"""
File upload and processing schemas with enhanced validation.

This module contains Pydantic models for file upload operations with
comprehensive validation for security, size limits, and content types.
"""

import base64
import mimetypes
from typing import Optional, List, Union
from pydantic import BaseModel, Field, validator
from fastapi import UploadFile

from .validators import (
    validate_filename,
    validate_file_size,
    validate_mime_type,
    ValidationConstants,
)


class FileUploadRequest(BaseModel):
    """Request model for file upload with enhanced validation."""

    filename: str = Field(..., description="Original filename")
    content: str = Field(..., description="Base64 encoded file content")
    mime_type: Optional[str] = Field(None, description="MIME type of the file")

    @validator('filename')
    def validate_filename_format(cls, v):
        """Validate filename format and security."""
        return validate_filename(v)

    @validator('content')
    def validate_file_content(cls, v):
        """Validate base64 encoded file content."""
        if not v:
            raise ValueError("File content cannot be empty")

        try:
            # Decode base64 to validate format and get size
            decoded = base64.b64decode(v, validate=True)

            # Validate file size
            validate_file_size(len(decoded))

            # Basic content validation
            if len(decoded) == 0:
                raise ValueError("File cannot be empty")

            return v

        except base64.binascii.Error:
            raise ValueError("Invalid base64 encoded content")
        except Exception as e:
            raise ValueError(f"File validation failed: {str(e)}")

    @validator('mime_type')
    def validate_mime_type_format(cls, v, values):
        """Validate MIME type if provided, or detect from filename."""
        filename = values.get('filename')

        if v:
            # Validate provided MIME type
            return validate_mime_type(v)
        elif filename:
            # Try to detect MIME type from filename
            detected_type, _ = mimetypes.guess_type(filename)
            if detected_type:
                try:
                    return validate_mime_type(detected_type)
                except ValueError:
                    # If detected type is not allowed, raise error
                    raise ValueError(f"File type not supported: {detected_type}")
            else:
                raise ValueError("Could not determine file type. Please specify mime_type.")
        else:
            raise ValueError("Either mime_type must be provided or filename must have an extension")

    class Config:
        json_schema_extra = {
            "example": {
                "filename": "document.txt",
                "content": "VGhpcyBpcyBhIHNhbXBsZSB0ZXh0IGZpbGU=",  # "This is a sample text file" in base64
                "mime_type": "text/plain"
            }
        }


class FileUploadResponse(BaseModel):
    """Response model for file upload operations."""

    filename: str = Field(..., description="Processed filename")
    size: int = Field(..., ge=0, description="File size in bytes")
    mime_type: str = Field(..., description="Detected/validated MIME type")
    file_id: Optional[str] = Field(None, description="Unique file identifier")

    class Config:
        json_schema_extra = {
            "example": {
                "filename": "document.txt",
                "size": 1024,
                "mime_type": "text/plain",
                "file_id": "file_123456"
            }
        }


class FileProcessingRequest(BaseModel):
    """Request model for file processing operations."""

    file_id: str = Field(..., description="File identifier")
    operation: str = Field(..., description="Processing operation to perform")
    parameters: Optional[dict] = Field(None, description="Operation-specific parameters")

    @validator('file_id')
    def validate_file_id(cls, v):
        """Validate file ID format."""
        if not v or not v.strip():
            raise ValueError("File ID cannot be empty")

        # Basic format validation
        if len(v) > 100:
            raise ValueError("File ID too long")

        return v.strip()

    @validator('operation')
    def validate_operation(cls, v):
        """Validate operation type."""
        allowed_operations = {
            'extract_text', 'summarize', 'translate', 'analyze'
        }

        if v not in allowed_operations:
            raise ValueError(f"Unsupported operation: {v}. Allowed: {', '.join(allowed_operations)}")

        return v


class FileValidationError(BaseModel):
    """File validation error details."""

    field: str = Field(..., description="Field that failed validation")
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Human-readable error message")

    class Config:
        json_schema_extra = {
            "example": {
                "field": "content",
                "error": "file_too_large",
                "message": "File size cannot exceed 10.0MB"
            }
        }


class SupportedFileTypesResponse(BaseModel):
    """Response model for supported file types information."""

    extensions: List[str] = Field(..., description="Supported file extensions")
    mime_types: List[str] = Field(..., description="Supported MIME types")
    max_size_mb: float = Field(..., description="Maximum file size in MB")

    @classmethod
    def get_supported_types(cls):
        """Get current supported file types configuration."""
        return cls(
            extensions=list(ValidationConstants.ALLOWED_FILE_EXTENSIONS),
            mime_types=list(ValidationConstants.ALLOWED_MIME_TYPES),
            max_size_mb=ValidationConstants.MAX_FILE_SIZE / (1024 * 1024)
        )

    class Config:
        json_schema_extra = {
            "example": {
                "extensions": [".txt", ".md", ".pdf", ".doc", ".docx"],
                "mime_types": ["text/plain", "text/markdown", "application/pdf"],
                "max_size_mb": 10.0
            }
        }


def validate_upload_file(file: UploadFile) -> dict:
    """
    Validate an uploaded file from FastAPI.

    Args:
        file: FastAPI UploadFile object

    Returns:
        Dictionary with validation results

    Raises:
        ValueError: If validation fails
    """
    errors = []

    # Validate filename
    try:
        validate_filename(file.filename)
    except ValueError as e:
        errors.append(FileValidationError(
            field="filename",
            error="invalid_filename",
            message=str(e)
        ))

    # Validate content type
    if file.content_type:
        try:
            validate_mime_type(file.content_type)
        except ValueError as e:
            errors.append(FileValidationError(
                field="content_type",
                error="unsupported_type",
                message=str(e)
            ))

    # Validate file size (if available)
    if hasattr(file, 'size') and file.size is not None:
        try:
            validate_file_size(file.size)
        except ValueError as e:
            errors.append(FileValidationError(
                field="size",
                error="file_too_large",
                message=str(e)
            ))

    if errors:
        raise ValueError(f"File validation failed: {[error.dict() for error in errors]}")

    return {
        "filename": file.filename,
        "content_type": file.content_type,
        "size": getattr(file, 'size', None)
    }


class FileMetadata(BaseModel):
    """File metadata model with validation."""

    filename: str = Field(..., description="Original filename")
    size: int = Field(..., ge=0, description="File size in bytes")
    mime_type: str = Field(..., description="MIME type")
    encoding: Optional[str] = Field(None, description="Text encoding (for text files)")
    language: Optional[str] = Field(None, description="Detected language (for text files)")
    page_count: Optional[int] = Field(None, ge=1, description="Number of pages (for documents)")
    word_count: Optional[int] = Field(None, ge=0, description="Word count (for text files)")

    @validator('filename')
    def validate_filename_format(cls, v):
        """Validate filename format."""
        return validate_filename(v)

    @validator('mime_type')
    def validate_mime_type_format(cls, v):
        """Validate MIME type."""
        return validate_mime_type(v)

    @validator('encoding')
    def validate_encoding(cls, v):
        """Validate text encoding."""
        if v is None:
            return v

        # Common text encodings
        allowed_encodings = {
            'utf-8', 'utf-16', 'utf-32', 'ascii', 'latin1', 'cp1252'
        }

        if v.lower() not in allowed_encodings:
            raise ValueError(f"Unsupported encoding: {v}")

        return v.lower()

    class Config:
        json_schema_extra = {
            "example": {
                "filename": "document.txt",
                "size": 2048,
                "mime_type": "text/plain",
                "encoding": "utf-8",
                "language": "en",
                "word_count": 350
            }
        }
