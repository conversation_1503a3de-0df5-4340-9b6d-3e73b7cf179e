from enum import Enum
from pydantic import BaseModel, HttpUrl

# Динамическое создание SummarizeMode на основе доступных режимов в MODEL_CONFIGS
from worker.summarizers.models_config_new import MODEL_CONFIGS


class TaskStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class FileType(str, Enum):
    TXT = "text/plain"
    MD = "text/markdown"


class MessageType(str, Enum):
    STATUS = "status_update"
    RESULT = "result"
    TEXT = "text_input"
    FILE = "file_input"


class SubtitleRequest(BaseModel):
    url: HttpUrl
    client_uid: str | None = None


class SubtitleResponse(BaseModel):
    status: TaskStatus
    task_id: str
    client_uid: str | None = None
    title: str | None = None
    original_language: str | None = None  # ISO 639-1 language code
    publish_date: str | None = None
    en_subtitles: str | None = None
    ru_subtitles: str | None = None
    error: str | None = None


class VideoListRequest(BaseModel):
    url: HttpUrl
    client_uid: str | None = None


class VideoListResponse(BaseModel):
    status: TaskStatus
    task_id: str
    client_uid: str | None = None
    video_ids: list[str] | None = None
    error: str | None = None


class SummarizeMode(str, Enum):
    # Автоматически создаем элементы Enum из ключей MODEL_CONFIGS
    # Используем dict comprehension для создания атрибутов
    locals().update({key.upper(): key for key in MODEL_CONFIGS.keys()})


class SummarizeRequest(BaseModel):
    client_uid: str | None = None
    mode: SummarizeMode | None = None
    og_text: str


class SummarizeResponse(BaseModel):
    status: TaskStatus
    task_id: str
    client_uid: str | None = None
    summary: str | None = None
    error: str | None = None


class WebSocketMessage(BaseModel):
    """Base WebSocket message"""

    type: MessageType
    status: TaskStatus | None = None
    task_id: str | None = None
    client_uid: str | None = None
    error: str | None = None


class WebSocketSubtitleMessage(WebSocketMessage):
    """Subtitle-specific WebSocket message"""

    title: str | None = None
    original_language: str | None = None  # ISO 639-1 language code
    publish_date: str | None = None
    en_subtitles: str | None = None
    ru_subtitles: str | None = None


class WebSocketSummarizeMessage(WebSocketMessage):
    """Summarize-specific WebSocket message"""

    summary: str | None = None


class WebSocketSummarizeRequest(BaseModel):
    """Request for summarization via WebSocket"""

    type: MessageType  # text_input or file_input
    client_uid: str | None = None  # Client UID
    mode: SummarizeMode | None = None  # Summarization mode
    content: str  # Text content or base64 encoded file content
    filename: str | None = None  # Required for file_input
