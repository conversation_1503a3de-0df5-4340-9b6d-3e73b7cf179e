"""
Custom validators for data validation across the application.

This module provides reusable validators for common data types and patterns
used throughout the YouTube subtitles and summarization API.
"""

import re
import urllib.parse
from typing import Any, Optional, List, Set
from pydantic import Field


class ValidationConstants:
    """Constants used for validation across the application."""

    # YouTube URL patterns
    YOUTUBE_DOMAINS = {
        'youtube.com', 'www.youtube.com', 'm.youtube.com',
        'youtu.be', 'www.youtu.be',
        'youtube-nocookie.com', 'www.youtube-nocookie.com'
    }

    # Client UID constraints
    CLIENT_UID_MAX_LENGTH = 100
    CLIENT_UID_MIN_LENGTH = 3
    CLIENT_UID_PATTERN = r'^[a-zA-Z0-9_-]+$'

    # Text content constraints
    MAX_TEXT_LENGTH = 1_000_000  # 1MB of text
    MIN_TEXT_LENGTH = 10

    # File constraints
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_EXTENSIONS = {'.txt', '.md', '.rtf', '.doc', '.docx', '.pdf'}
    ALLOWED_MIME_TYPES = {
        'text/plain', 'text/markdown', 'text/rtf',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/pdf'
    }

    # Language codes (ISO 639-1)
    SUPPORTED_LANGUAGE_CODES = {
        'en', 'ru', 'es', 'fr', 'de', 'it', 'pt', 'ja', 'ko', 'zh', 'ar', 'hi'
    }


def validate_youtube_url(url: str) -> str:
    """
    Validate that the URL is a valid YouTube URL.

    Args:
        url: The URL string to validate

    Returns:
        The validated URL string

    Raises:
        ValueError: If the URL is not a valid YouTube URL
    """
    if not url:
        raise ValueError("URL cannot be empty")

    try:
        parsed = urllib.parse.urlparse(url)
    except Exception:
        raise ValueError("Invalid URL format")

    # Check if domain is YouTube
    domain = parsed.netloc.lower()
    if domain not in ValidationConstants.YOUTUBE_DOMAINS:
        raise ValueError(
            f"URL must be from a YouTube domain. "
            f"Supported domains: {', '.join(ValidationConstants.YOUTUBE_DOMAINS)}"
        )

    # Check for video ID in various YouTube URL formats
    video_id = None

    if domain in ['youtu.be', 'www.youtu.be']:
        # Short URL format: https://youtu.be/VIDEO_ID
        video_id = parsed.path.lstrip('/')
    elif 'youtube.com' in domain or 'youtube-nocookie.com' in domain:
        # Long URL format: https://www.youtube.com/watch?v=VIDEO_ID
        query_params = urllib.parse.parse_qs(parsed.query)
        if 'v' in query_params:
            video_id = query_params['v'][0]
        elif parsed.path.startswith('/embed/'):
            video_id = parsed.path.split('/embed/')[-1].split('?')[0]  # Remove query params
        elif parsed.path.startswith('/v/'):
            video_id = parsed.path.split('/v/')[-1].split('?')[0]  # Remove query params

    if not video_id:
        raise ValueError("YouTube URL must contain a valid video ID")

    # Validate video ID format (YouTube video IDs are 11 characters)
    if not re.match(r'^[a-zA-Z0-9_-]{11}$', video_id):
        raise ValueError("Invalid YouTube video ID format")

    return url


def validate_client_uid(uid: Optional[str]) -> Optional[str]:
    """
    Validate client UID format and constraints.

    Args:
        uid: The client UID to validate

    Returns:
        The validated UID or None

    Raises:
        ValueError: If the UID format is invalid
    """
    if uid is None:
        return None

    if not isinstance(uid, str):
        raise ValueError("Client UID must be a string")

    # Check length constraints
    if len(uid) < ValidationConstants.CLIENT_UID_MIN_LENGTH:
        raise ValueError(
            f"Client UID must be at least {ValidationConstants.CLIENT_UID_MIN_LENGTH} characters long"
        )

    if len(uid) > ValidationConstants.CLIENT_UID_MAX_LENGTH:
        raise ValueError(
            f"Client UID must be no more than {ValidationConstants.CLIENT_UID_MAX_LENGTH} characters long"
        )

    # Check character pattern
    if not re.match(ValidationConstants.CLIENT_UID_PATTERN, uid):
        raise ValueError(
            "Client UID can only contain letters, numbers, underscores, and hyphens"
        )

    return uid


def validate_text_content(text: str, min_length: Optional[int] = None, max_length: Optional[int] = None) -> str:
    """
    Validate text content for length and basic format.

    Args:
        text: The text to validate
        min_length: Minimum allowed length (optional)
        max_length: Maximum allowed length (optional)

    Returns:
        The validated text

    Raises:
        ValueError: If the text doesn't meet validation criteria
    """
    if not isinstance(text, str):
        raise ValueError("Text content must be a string")

    # Remove leading/trailing whitespace for length calculation
    stripped_text = text.strip()

    if not stripped_text:
        raise ValueError("Text content cannot be empty or only whitespace")

    # Check minimum length
    min_len = min_length or ValidationConstants.MIN_TEXT_LENGTH
    if len(stripped_text) < min_len:
        raise ValueError(f"Text content must be at least {min_len} characters long")

    # Check maximum length
    max_len = max_length or ValidationConstants.MAX_TEXT_LENGTH
    if len(text) > max_len:
        raise ValueError(f"Text content must be no more than {max_len} characters long")

    # Check for suspicious patterns (basic security)
    suspicious_patterns = [
        r'<script[^>]*>.*?</script>',  # Script tags
        r'javascript:',  # JavaScript URLs
        r'data:.*base64',  # Base64 data URLs (potential XSS)
    ]

    for pattern in suspicious_patterns:
        if re.search(pattern, text, re.IGNORECASE | re.DOTALL):
            raise ValueError("Text content contains potentially unsafe content")

    return text


def validate_language_code(code: Optional[str]) -> Optional[str]:
    """
    Validate ISO 639-1 language code.

    Args:
        code: The language code to validate

    Returns:
        The validated language code or None

    Raises:
        ValueError: If the language code is invalid
    """
    if code is None:
        return None

    if not isinstance(code, str):
        raise ValueError("Language code must be a string")

    code = code.lower().strip()

    if len(code) != 2:
        raise ValueError("Language code must be exactly 2 characters (ISO 639-1)")

    if code not in ValidationConstants.SUPPORTED_LANGUAGE_CODES:
        raise ValueError(
            f"Unsupported language code '{code}'. "
            f"Supported codes: {', '.join(sorted(ValidationConstants.SUPPORTED_LANGUAGE_CODES))}"
        )

    return code


def validate_filename(filename: Optional[str]) -> Optional[str]:
    """
    Validate filename for security and format.

    Args:
        filename: The filename to validate

    Returns:
        The validated filename or None

    Raises:
        ValueError: If the filename is invalid
    """
    if filename is None:
        return None

    if not isinstance(filename, str):
        raise ValueError("Filename must be a string")

    filename = filename.strip()

    if not filename:
        raise ValueError("Filename cannot be empty")

    # Check for path traversal attempts
    if '..' in filename or '/' in filename or '\\' in filename:
        raise ValueError("Filename cannot contain path separators or parent directory references")

    # Check for reserved characters
    reserved_chars = '<>:"|?*'
    if any(char in filename for char in reserved_chars):
        raise ValueError(f"Filename cannot contain reserved characters: {reserved_chars}")

    # Check length
    if len(filename) > 255:
        raise ValueError("Filename must be no more than 255 characters long")

    # Check file extension
    if '.' in filename:
        extension = '.' + filename.split('.')[-1].lower()
        if extension not in ValidationConstants.ALLOWED_FILE_EXTENSIONS:
            raise ValueError(
                f"File extension '{extension}' is not allowed. "
                f"Allowed extensions: {', '.join(ValidationConstants.ALLOWED_FILE_EXTENSIONS)}"
            )

    return filename


def validate_file_size(size: int) -> int:
    """
    Validate file size constraints.

    Args:
        size: File size in bytes

    Returns:
        The validated file size

    Raises:
        ValueError: If the file size is invalid
    """
    if not isinstance(size, int) or size < 0:
        raise ValueError("File size must be a non-negative integer")

    if size == 0:
        raise ValueError("File cannot be empty")

    if size > ValidationConstants.MAX_FILE_SIZE:
        max_size_mb = ValidationConstants.MAX_FILE_SIZE / (1024 * 1024)
        raise ValueError(f"File size cannot exceed {max_size_mb:.1f}MB")

    return size


def validate_mime_type(mime_type: str) -> str:
    """
    Validate MIME type for uploaded files.

    Args:
        mime_type: The MIME type to validate

    Returns:
        The validated MIME type

    Raises:
        ValueError: If the MIME type is not allowed
    """
    if not isinstance(mime_type, str):
        raise ValueError("MIME type must be a string")

    mime_type = mime_type.lower().strip()

    if mime_type not in ValidationConstants.ALLOWED_MIME_TYPES:
        raise ValueError(
            f"MIME type '{mime_type}' is not allowed. "
            f"Allowed types: {', '.join(ValidationConstants.ALLOWED_MIME_TYPES)}"
        )

    return mime_type


# Pydantic field factories for common validation patterns
def youtube_url_field(**kwargs):
    """Create a Pydantic field for YouTube URLs with validation."""
    return Field(
        ...,
        description="Valid YouTube URL (youtube.com, youtu.be, etc.)",
        example="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        **kwargs
    )


def client_uid_field(**kwargs):
    """Create a Pydantic field for client UIDs with validation."""
    return Field(
        None,
        min_length=ValidationConstants.CLIENT_UID_MIN_LENGTH,
        max_length=ValidationConstants.CLIENT_UID_MAX_LENGTH,
        pattern=ValidationConstants.CLIENT_UID_PATTERN,
        description="Client identifier (3-100 chars, alphanumeric, underscore, hyphen only)",
        example="user_123",
        **kwargs
    )


def text_content_field(min_length: Optional[int] = None, max_length: Optional[int] = None, **kwargs):
    """Create a Pydantic field for text content with validation."""
    min_len = min_length or ValidationConstants.MIN_TEXT_LENGTH
    max_len = max_length or ValidationConstants.MAX_TEXT_LENGTH

    # Set default description if not provided
    if 'description' not in kwargs:
        kwargs['description'] = f"Text content ({min_len}-{max_len} chars)"

    return Field(
        ...,
        min_length=min_len,
        max_length=max_len,
        **kwargs
    )
