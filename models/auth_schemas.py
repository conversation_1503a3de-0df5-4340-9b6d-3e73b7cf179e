"""
Authentication and authorization schemas with enhanced validation.

This module contains Pydantic models for API key management, user authentication,
and authorization with comprehensive validation rules.
"""

import re
from datetime import datetime
from typing import Optional, List, Set
from pydantic import BaseModel, Field, validator
from enum import Enum

from .validators import (
    validate_client_uid,
    client_uid_field,
    ValidationConstants,
)


class Permission(str, Enum):
    """Available permissions for API keys."""
    READ = "read"
    WRITE = "write"
    ADMIN = "admin"


class APIKeyStatus(str, Enum):
    """Status of an API key."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    REVOKED = "revoked"
    EXPIRED = "expired"


class APIKeyRequest(BaseModel):
    """Request model for creating a new API key with enhanced validation."""

    name: str = Field(
        ...,
        min_length=3,
        max_length=100,
        description="Human-readable name for the API key",
        example="My Application Key"
    )
    permissions: List[Permission] = Field(
        ...,
        min_items=1,
        max_items=3,
        description="List of permissions for this API key",
        example=["read", "write"]
    )
    description: Optional[str] = Field(
        None,
        max_length=500,
        description="Optional description of the API key usage",
        example="API key for my mobile application"
    )
    expires_in_days: Optional[int] = Field(
        None,
        ge=1,
        le=365,
        description="Number of days until the key expires (1-365 days, None for no expiration)",
        example=90
    )

    @validator('name')
    def validate_name(cls, v):
        """Validate API key name format."""
        if not v.strip():
            raise ValueError("API key name cannot be empty or only whitespace")

        # Check for potentially problematic characters
        if re.search(r'[<>"\']', v):
            raise ValueError("API key name cannot contain HTML/script characters")

        return v.strip()

    @validator('permissions')
    def validate_permissions(cls, v):
        """Validate permissions list."""
        if not v:
            raise ValueError("At least one permission must be specified")

        # Remove duplicates while preserving order
        seen = set()
        unique_permissions = []
        for perm in v:
            if perm not in seen:
                seen.add(perm)
                unique_permissions.append(perm)

        return unique_permissions

    @validator('description')
    def validate_description(cls, v):
        """Validate description format."""
        if v is None:
            return v

        v = v.strip()
        if not v:
            return None

        # Check for potentially problematic characters
        if re.search(r'[<>"\']', v):
            raise ValueError("Description cannot contain HTML/script characters")

        return v

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Mobile App Key",
                "permissions": ["read", "write"],
                "description": "API key for mobile application access",
                "expires_in_days": 90
            }
        }


class APIKeyResponse(BaseModel):
    """Response model for API key operations with enhanced validation."""

    id: str = Field(..., description="Unique API key identifier")
    name: str = Field(..., description="Human-readable name")
    key: Optional[str] = Field(None, description="API key value (only shown on creation)")
    permissions: List[Permission] = Field(..., description="List of permissions")
    status: APIKeyStatus = Field(..., description="Current status of the key")
    description: Optional[str] = Field(None, description="Description of the key usage")
    created_at: datetime = Field(..., description="Creation timestamp")
    expires_at: Optional[datetime] = Field(None, description="Expiration timestamp")
    last_used_at: Optional[datetime] = Field(None, description="Last usage timestamp")
    usage_count: int = Field(0, ge=0, description="Number of times the key has been used")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        json_schema_extra = {
            "example": {
                "id": "key_123456",
                "name": "Mobile App Key",
                "permissions": ["read", "write"],
                "status": "active",
                "description": "API key for mobile application access",
                "created_at": "2024-01-15T10:30:00Z",
                "expires_at": "2024-04-15T10:30:00Z",
                "last_used_at": "2024-01-14T15:45:00Z",
                "usage_count": 42
            }
        }


class APIKeyUpdateRequest(BaseModel):
    """Request model for updating an existing API key with enhanced validation."""

    name: Optional[str] = Field(
        None,
        min_length=3,
        max_length=100,
        description="New name for the API key"
    )
    permissions: Optional[List[Permission]] = Field(
        None,
        min_items=1,
        max_items=3,
        description="New list of permissions"
    )
    description: Optional[str] = Field(
        None,
        max_length=500,
        description="New description"
    )
    status: Optional[APIKeyStatus] = Field(
        None,
        description="New status (active/inactive/revoked)"
    )

    @validator('name')
    def validate_name(cls, v):
        """Validate API key name format."""
        if v is None:
            return v

        if not v.strip():
            raise ValueError("API key name cannot be empty or only whitespace")

        # Check for potentially problematic characters
        if re.search(r'[<>"\']', v):
            raise ValueError("API key name cannot contain HTML/script characters")

        return v.strip()

    @validator('permissions')
    def validate_permissions(cls, v):
        """Validate permissions list."""
        if v is None:
            return v

        if not v:
            raise ValueError("At least one permission must be specified")

        # Remove duplicates while preserving order
        seen = set()
        unique_permissions = []
        for perm in v:
            if perm not in seen:
                seen.add(perm)
                unique_permissions.append(perm)

        return unique_permissions

    @validator('description')
    def validate_description(cls, v):
        """Validate description format."""
        if v is None:
            return v

        v = v.strip()
        if not v:
            return None

        # Check for potentially problematic characters
        if re.search(r'[<>"\']', v):
            raise ValueError("Description cannot contain HTML/script characters")

        return v

    @validator('status')
    def validate_status_transition(cls, v):
        """Validate status transitions."""
        if v is None:
            return v

        # Note: Additional business logic for status transitions
        # should be implemented in the service layer
        if v == APIKeyStatus.REVOKED:
            # Revoked keys cannot be changed back
            pass

        return v


class APIKeyListResponse(BaseModel):
    """Response model for listing API keys with enhanced validation."""

    keys: List[APIKeyResponse] = Field(..., description="List of API keys")
    total: int = Field(..., ge=0, description="Total number of keys")
    active: int = Field(..., ge=0, description="Number of active keys")
    inactive: int = Field(..., ge=0, description="Number of inactive keys")
    revoked: int = Field(..., ge=0, description="Number of revoked keys")

    @validator('keys')
    def validate_keys_list(cls, v):
        """Validate the keys list."""
        if len(v) > 1000:  # Reasonable limit
            raise ValueError("Too many keys in response")

        return v

    class Config:
        json_schema_extra = {
            "example": {
                "keys": [
                    {
                        "id": "key_123456",
                        "name": "Mobile App Key",
                        "permissions": ["read", "write"],
                        "status": "active",
                        "created_at": "2024-01-15T10:30:00Z",
                        "usage_count": 42
                    }
                ],
                "total": 1,
                "active": 1,
                "inactive": 0,
                "revoked": 0
            }
        }


class UserInfo(BaseModel):
    """User information model with enhanced validation."""

    id: Optional[str] = Field(None, description="User identifier")
    name: str = Field(..., description="User name")
    permissions: List[Permission] = Field(..., description="User permissions")
    api_key_id: Optional[str] = Field(None, description="API key used for authentication")

    @validator('name')
    def validate_name(cls, v):
        """Validate user name."""
        if not v or not v.strip():
            raise ValueError("User name cannot be empty")

        return v.strip()

    class Config:
        json_schema_extra = {
            "example": {
                "id": "user_123",
                "name": "demo_user",
                "permissions": ["read", "write"],
                "api_key_id": "key_123456"
            }
        }


class AuthenticationError(BaseModel):
    """Authentication error response model."""

    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[dict] = Field(None, description="Additional error details")

    class Config:
        json_schema_extra = {
            "example": {
                "error": "invalid_api_key",
                "message": "The provided API key is invalid or has been revoked",
                "details": {
                    "key_id": "key_123456",
                    "status": "revoked"
                }
            }
        }
