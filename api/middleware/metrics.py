"""
Metrics collection middleware.
"""
import time
from typing import Dict, Any
from fastapi import Request, Response
from loguru import logger


class MetricsCollector:
    """Simple in-memory metrics collector."""
    
    def __init__(self):
        self.request_count: Dict[str, int] = {}
        self.request_duration: Dict[str, list] = {}
        self.error_count: Dict[str, int] = {}
        self.status_codes: Dict[int, int] = {}
        self.active_requests = 0
        self.total_requests = 0
        
    def record_request_start(self, method: str, path: str):
        """Record the start of a request."""
        self.active_requests += 1
        self.total_requests += 1
        
        endpoint = f"{method} {path}"
        self.request_count[endpoint] = self.request_count.get(endpoint, 0) + 1
        
    def record_request_end(self, method: str, path: str, status_code: int, duration: float):
        """Record the end of a request."""
        self.active_requests = max(0, self.active_requests - 1)
        
        endpoint = f"{method} {path}"
        
        # Record duration
        if endpoint not in self.request_duration:
            self.request_duration[endpoint] = []
        self.request_duration[endpoint].append(duration)
        
        # Keep only last 1000 durations per endpoint
        if len(self.request_duration[endpoint]) > 1000:
            self.request_duration[endpoint] = self.request_duration[endpoint][-1000:]
        
        # Record status codes
        self.status_codes[status_code] = self.status_codes.get(status_code, 0) + 1
        
        # Record errors (4xx and 5xx)
        if status_code >= 400:
            self.error_count[endpoint] = self.error_count.get(endpoint, 0) + 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics."""
        # Calculate average durations
        avg_durations = {}
        for endpoint, durations in self.request_duration.items():
            if durations:
                avg_durations[endpoint] = sum(durations) / len(durations)
        
        return {
            "active_requests": self.active_requests,
            "total_requests": self.total_requests,
            "request_count_by_endpoint": dict(self.request_count),
            "average_duration_by_endpoint": avg_durations,
            "error_count_by_endpoint": dict(self.error_count),
            "status_codes": dict(self.status_codes),
        }
    
    def reset_metrics(self):
        """Reset all metrics."""
        self.request_count.clear()
        self.request_duration.clear()
        self.error_count.clear()
        self.status_codes.clear()
        self.active_requests = 0
        self.total_requests = 0


# Global metrics collector instance
metrics_collector = MetricsCollector()


async def metrics_middleware(request: Request, call_next):
    """Middleware to collect request metrics."""
    start_time = time.time()
    
    # Normalize path for metrics (remove path parameters)
    path = request.url.path
    method = request.method
    
    # Record request start
    metrics_collector.record_request_start(method, path)
    
    try:
        # Process request
        response = await call_next(request)
        
        # Calculate duration
        duration = time.time() - start_time
        
        # Record request end
        metrics_collector.record_request_end(method, path, response.status_code, duration)
        
        # Add metrics headers
        response.headers["X-Response-Time"] = f"{duration:.3f}s"
        
        # Log slow requests
        if duration > 5.0:  # Log requests taking more than 5 seconds
            logger.warning(
                f"Slow request: {method} {path} took {duration:.3f}s",
                extra={
                    "method": method,
                    "path": path,
                    "duration": duration,
                    "status_code": response.status_code,
                }
            )
        
        return response
        
    except Exception as e:
        # Calculate duration even for failed requests
        duration = time.time() - start_time
        
        # Record as 500 error
        metrics_collector.record_request_end(method, path, 500, duration)
        
        logger.error(
            f"Request failed: {method} {path} after {duration:.3f}s - {str(e)}",
            extra={
                "method": method,
                "path": path,
                "duration": duration,
                "error": str(e),
            }
        )
        
        # Re-raise the exception
        raise


def get_metrics() -> Dict[str, Any]:
    """Get current metrics."""
    return metrics_collector.get_metrics()


def reset_metrics():
    """Reset all metrics."""
    metrics_collector.reset_metrics()
