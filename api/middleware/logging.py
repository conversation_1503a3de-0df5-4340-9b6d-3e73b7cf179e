"""
Structured logging middleware for FastAPI.

This middleware adds correlation IDs to requests and provides structured logging
with context information for better observability and debugging.
"""

import contextlib
import time
import uuid
from typing import Callable, Optional

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from core.config import get_settings

settings = get_settings()


class StructuredLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware that adds structured logging with correlation IDs to all requests.

    Features:
    - Generates unique correlation ID for each request
    - Logs request/response information in structured format
    - Adds user context from authentication
    - Measures request processing time
    - Filters sensitive information from logs
    """

    def __init__(
        self,
        app,
        logger: Optional[structlog.BoundLogger] = None,
        exclude_paths: Optional[list] = None,
        log_request_body: bool = False,
        log_response_body: bool = False,
    ):
        """
        Initialize the logging middleware.

        Args:
            app: FastAPI application instance
            logger: Structured logger instance (optional)
            exclude_paths: List of paths to exclude from logging
            log_request_body: Whether to log request body (be careful with sensitive data)
            log_response_body: Whether to log response body (be careful with large responses)
        """
        super().__init__(app)

        # Initialize logger with fallback
        if logger:
            self.logger = logger
        else:
            try:
                self.logger = structlog.get_logger(__name__)
            except Exception:
                # Fallback to loguru if structlog is not available
                from loguru import logger as fallback_logger

                self.logger = fallback_logger

        self.exclude_paths = exclude_paths or [
            "/health",
            "/metrics",
            "/docs",
            "/redoc",
            "/openapi.json",
        ]
        self.log_request_body = log_request_body
        self.log_response_body = log_response_body

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request and add structured logging.

        Args:
            request: FastAPI request object
            call_next: Next middleware/handler in the chain

        Returns:
            Response object
        """
        # Skip logging for excluded paths
        if request.url.path in self.exclude_paths:
            return await call_next(request)

        # Generate correlation ID
        correlation_id = str(uuid.uuid4())

        # Extract user information from request state (set by auth middleware)
        user_info = self._extract_user_info(request)

        # Create a context manager for the request
        @contextlib.contextmanager
        def request_context():
            try:
                # Bind context variables for structured logging
                structlog.contextvars.clear_contextvars()
                context_vars = structlog.contextvars.bind_contextvars(
                    correlation_id=correlation_id,
                    user_id=user_info.get("id"),
                    user_name=user_info.get("name"),
                    user_permissions=user_info.get("permissions"),
                    request_id=correlation_id,  # Alias for correlation_id
                )
                yield context_vars
            except Exception as e:
                # Fallback to no context if there's an error
                self.logger.warning("Failed to set up logging context", error=str(e))
                yield {}
            finally:
                # Ensure context is always cleared
                structlog.contextvars.clear_contextvars()

        # Use the context manager for the request
        with request_context():
            # Log request start
            start_time = time.time()

            request_data = {
                "event": "request_started",
                "method": request.method,
                "url": str(request.url),
                "path": request.url.path,
                "query_params": dict(request.query_params),
                "headers": self._filter_headers(dict(request.headers)),
                "client_ip": self._get_client_ip(request),
                "user_agent": request.headers.get("user-agent"),
            }

            # Add request body if enabled (be careful with sensitive data)
            if self.log_request_body and request.method in ["POST", "PUT", "PATCH"]:
                try:
                    body = await request.body()
                    if body:
                        request_data["request_body_size"] = len(body)
                        # Only log body for small requests to avoid memory issues
                        if len(body) < 1024:  # 1KB limit
                            request_data["request_body"] = body.decode(
                                "utf-8", errors="ignore"
                            )
                except Exception as e:
                    request_data["request_body_error"] = str(e)

            # Log request start with fallback
            try:
                self.logger.info(**request_data)
            except Exception:
                # Fallback to simple logging if structlog fails
                from loguru import logger as fallback_logger

                fallback_logger.info(
                    f"Request started: {request.method} {request.url.path}"
                )

            # Process request
            try:
                response = await call_next(request)

                # Calculate processing time
                process_time = time.time() - start_time

                # Log successful response
                response_data = {
                    "event": "request_completed",
                    "status_code": response.status_code,
                    "process_time": round(process_time, 4),
                    "response_headers": self._filter_headers(dict(response.headers)),
                }

                # Add response body if enabled and status indicates success
                if (
                    self.log_response_body
                    and response.status_code < 400
                    and hasattr(response, "body")
                ):
                    try:
                        if hasattr(response, "body") and response.body:
                            response_data["response_body_size"] = len(response.body)
                    except Exception as e:
                        response_data["response_body_error"] = str(e)

                # Log with appropriate level based on status code
                try:
                    if response.status_code >= 500:
                        self.logger.error(**response_data)
                    elif response.status_code >= 400:
                        self.logger.warning(**response_data)
                    else:
                        self.logger.info(**response_data)
                except Exception:
                    # Fallback to simple logging if structlog fails
                    from loguru import logger as fallback_logger

                    fallback_logger.info(
                        f"Request completed: {request.method} {request.url.path} - {response.status_code}"
                    )

                return response

            except Exception as exc:
                # Calculate processing time for failed requests
                process_time = time.time() - start_time

                # Log exception
                error_data = {
                    "event": "request_failed",
                    "error": str(exc),
                    "error_type": type(exc).__name__,
                    "process_time": round(process_time, 4),
                }

                try:
                    self.logger.error(**error_data)
                except Exception:
                    # Fallback to simple logging if structlog fails
                    from loguru import logger as fallback_logger

                    fallback_logger.error(
                        f"Request failed: {request.method} {request.url.path} - {str(exc)}"
                    )
                raise

    def _extract_user_info(self, request: Request) -> dict:
        """
        Extract user information from request state.

        Args:
            request: FastAPI request object

        Returns:
            Dictionary with user information
        """
        user = getattr(request.state, "user", {})
        if isinstance(user, dict):
            return {
                "id": user.get("id"),
                "name": user.get("name", "anonymous"),
                "permissions": user.get("permissions", []),
            }
        return {"id": None, "name": "anonymous", "permissions": []}

    def _get_client_ip(self, request: Request) -> str:
        """
        Get client IP address from request.

        Args:
            request: FastAPI request object

        Returns:
            Client IP address
        """
        # Check for forwarded headers first (for reverse proxies)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip

        # Fall back to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host

        return "unknown"

    def _filter_headers(self, headers: dict) -> dict:
        """
        Filter sensitive information from headers.

        Args:
            headers: Dictionary of headers

        Returns:
            Filtered headers dictionary
        """
        sensitive_headers = {
            "authorization",
            "cookie",
            "x-api-key",
            "x-auth-token",
            "x-csrf-token",
            "x-session-id",
            "set-cookie",
        }

        filtered = {}
        for key, value in headers.items():
            if key.lower() in sensitive_headers:
                filtered[key] = "[REDACTED]"
            else:
                filtered[key] = value

        return filtered


def create_logging_middleware(
    exclude_paths: Optional[list] = None,
    log_request_body: bool = False,
    log_response_body: bool = False,
) -> StructuredLoggingMiddleware:
    """
    Factory function to create logging middleware with default settings.

    Args:
        exclude_paths: List of paths to exclude from logging
        log_request_body: Whether to log request body
        log_response_body: Whether to log response body

    Returns:
        Configured logging middleware instance
    """
    # Try to get structured logger, fallback to loguru
    try:
        logger = structlog.get_logger("api.middleware.logging")
    except Exception:
        from loguru import logger

    default_exclude_paths = [
        "/health",
        "/metrics",
        "/docs",
        "/redoc",
        "/openapi.json",
        "/favicon.ico",
        "/robots.txt",
    ]

    if exclude_paths:
        default_exclude_paths.extend(exclude_paths)

    return StructuredLoggingMiddleware(
        app=None,  # Will be set by FastAPI
        logger=logger,
        exclude_paths=default_exclude_paths,
        log_request_body=log_request_body,
        log_response_body=log_response_body,
    )
