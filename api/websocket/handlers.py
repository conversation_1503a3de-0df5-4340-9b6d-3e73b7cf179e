"""WebSocket connection handlers for different types of connections."""

import asyncio
import logging
import json
from typing import Dict, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse

from models.schemas import (
    WebSocketSubtitleMessage,
    WebSocketSummarizeMessage,
    WebSocketSummarizeRequest,
    MessageType,
    TaskStatus,
)
from worker.queue import TaskQueue
from .manager import websocket_manager, ConnectionType

logger = logging.getLogger(__name__)
PING_INTERVAL = 30  # seconds

class WebSocketHandler:
    """Base class for WebSocket connection handlers."""
    
    def __init__(self, websocket: WebSocket, connection_type: ConnectionType, task_queue: TaskQueue):
        self.websocket = websocket
        self.connection_type = connection_type
        self.task_queue = task_queue
        self.client_uid: Optional[str] = None
        self.active = True
        
    async def handle_connection(self):
        """Handle the WebSocket connection lifecycle."""
        try:
            await websocket_manager.connect(self.websocket, self.connection_type)
            await self._send_connection_established()
            
            # Start ping task
            ping_task = asyncio.create_task(self._send_ping())
            
            # Handle incoming messages
            while self.active:
                try:
                    data = await self.websocket.receive_json()
                    await self._handle_message(data)
                except json.JSONDecodeError:
                    logger.warning("Received invalid JSON from WebSocket")
                    await self._send_error("Invalid JSON format")
                    
        except WebSocketDisconnect:
            logger.info("WebSocket disconnected")
        except Exception as e:
            logger.error(f"WebSocket error: {e}", exc_info=True)
        finally:
            ping_task.cancel()
            websocket_manager.disconnect(self.websocket, self.connection_type)
            await self._cleanup()
    
    async def _send_connection_established(self):
        """Send a connection established message to the client."""
        raise NotImplementedError
        
    async def _handle_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket message."""
        raise NotImplementedError
        
    async def _send_ping(self):
        """Send periodic ping to keep the connection alive."""
        while self.active:
            try:
                await asyncio.sleep(PING_INTERVAL)
                await self.websocket.send_json({"type": "ping"})
            except Exception as e:
                logger.error(f"Error sending ping: {e}")
                self.active = False
                break
    
    async def _send_error(self, error: str):
        """Send an error message to the client."""
        await self.websocket.send_json({"type": "error", "message": error})
    
    async def _cleanup(self):
        """Clean up resources when connection is closed."""
        pass


class SubtitleWebSocketHandler(WebSocketHandler):
    """Handler for subtitle WebSocket connections."""
    
    def __init__(self, websocket: WebSocket, task_queue: TaskQueue):
        super().__init__(websocket, ConnectionType.SUBTITLES, task_queue)
        self.task_id: Optional[str] = None
    
    async def _send_connection_established(self):
        """Send a connection established message to the client."""
        await self.websocket.send_json({
            "type": "connection_established",
            "message": "Connected to subtitle WebSocket"
        })
    
    async def _handle_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket message for subtitle requests."""
        try:
            message = WebSocketSubtitleMessage(**data)
            
            if message.type == MessageType.URL:
                await self._handle_url_message(message)
            else:
                await self._send_error(f"Unsupported message type: {message.type}")
                
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}", exc_info=True)
            await self._send_error(f"Error processing message: {str(e)}")
    
    async def _handle_url_message(self, message):
        """Handle URL message from WebSocket client."""
        self.client_uid = message.client_uid
        
        try:
            # Add task to queue
            response = await self.task_queue.add_subtitle_task(
                message.url,
                client_uid=message.client_uid,
                websocket=self.websocket
            )
            
            self.task_id = response.task_id
            
            # Send task ID back to client
            await self.websocket.send_json({
                "type": "task_created",
                "task_id": response.task_id,
                "status": response.status
            })
            
        except Exception as e:
            logger.error(f"Error processing URL: {e}", exc_info=True)
            await self._send_error(f"Error processing URL: {str(e)}")
    
    async def _cleanup(self):
        """Clean up resources when connection is closed."""
        if self.task_id:
            # Cancel the task if it's still running
            await self.task_queue.cancel_task(self.task_id)


class SummarizeWebSocketHandler(WebSocketHandler):
    """Handler for text summarization WebSocket connections."""
    
    def __init__(self, websocket: WebSocket, task_queue: TaskQueue):
        super().__init__(websocket, ConnectionType.SUMMARIZE, task_queue)
        self.task_id: Optional[str] = None
    
    async def _send_connection_established(self):
        """Send a connection established message to the client."""
        await self.websocket.send_json({
            "type": "connection_established",
            "message": "Connected to summarize WebSocket"
        })
    
    async def _handle_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket message for summarization requests."""
        try:
            if data.get("type") == "text":
                message = WebSocketSummarizeRequest(**data)
                await self._handle_text_message(message)
            elif data.get("type") == "file":
                await self._handle_file_message(data)
            else:
                await self._send_error(f"Unsupported message type: {data.get('type')}")
                
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}", exc_info=True)
            await self._send_error(f"Error processing message: {str(e)}")
    
    async def _handle_text_message(self, message: WebSocketSummarizeRequest):
        """Handle text message from WebSocket client."""
        self.client_uid = message.client_uid
        
        try:
            # Add task to queue
            response = await self.task_queue.add_summarize_task(
                text=message.text,
                mode=message.mode,
                client_uid=message.client_uid,
                websocket=self.websocket
            )
            
            self.task_id = response.task_id
            
            # Send task ID back to client
            await self.websocket.send_json({
                "type": "task_created",
                "task_id": response.task_id,
                "status": response.status
            })
            
        except Exception as e:
            logger.error(f"Error processing text: {e}", exc_info=True)
            await self._send_error(f"Error processing text: {str(e)}")
    
    async def _handle_file_message(self, data: Dict[str, Any]):
        """Handle file upload message from WebSocket client."""
        # This is a simplified version - actual file handling would need to be implemented
        # based on how the WebSocket client sends file data
        await self._send_error("File upload via WebSocket is not yet implemented")
    
    async def _cleanup(self):
        """Clean up resources when connection is closed."""
        if self.task_id:
            # Cancel the task if it's still running
            await self.task_queue.cancel_task(self.task_id)
