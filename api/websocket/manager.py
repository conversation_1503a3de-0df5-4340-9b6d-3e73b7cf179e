"""WebSocket connection manager for handling multiple client connections."""

from typing import Dict, List, Optional
import logging
from fastapi import WebSocket
from enum import Enum, auto

class ConnectionType(Enum):
    SUBTITLES = auto()
    SUMMARIZE = auto()

class ConnectionManager:
    """Manages active WebSocket connections."""
    
    def __init__(self):
        self.active_connections: Dict[ConnectionType, List[WebSocket]] = {
            ConnectionType.SUBTITLES: [],
            ConnectionType.SUMMARIZE: []
        }
        self.logger = logging.getLogger(__name__)

    async def connect(self, websocket: WebSocket, connection_type: ConnectionType):
        """Accept a new WebSocket connection and add it to the active connections."""
        await websocket.accept()
        self.active_connections[connection_type].append(websocket)
        self.logger.debug(f"New {connection_type.name} WebSocket connection accepted. "
                        f"Active connections: {len(self.active_connections[connection_type])}")

    def disconnect(self, websocket: WebSocket, connection_type: ConnectionType):
        """Remove a WebSocket connection from active connections."""
        try:
            self.active_connections[connection_type].remove(websocket)
            self.logger.debug(f"WebSocket connection removed. "
                           f"Active {connection_type.name} connections: "
                           f"{len(self.active_connections[connection_type])}")
        except ValueError:
            self.logger.warning("Attempted to remove non-existent WebSocket connection")

    async def broadcast(self, message: dict, connection_type: ConnectionType):
        """Send a message to all active connections of a specific type."""
        if not self.active_connections[connection_type]:
            return
            
        for connection in self.active_connections[connection_type]:
            try:
                await connection.send_json(message)
            except Exception as e:
                self.logger.error(f"Error broadcasting to WebSocket: {e}", exc_info=True)

    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_json(message)
        except Exception as e:
            self.logger.error(f"Error sending message to WebSocket: {e}", exc_info=True)

    def get_active_connections_count(self, connection_type: ConnectionType) -> int:
        """Get the number of active connections of a specific type."""
        return len(self.active_connections[connection_type])

# Global WebSocket manager instance
websocket_manager = ConnectionManager()
