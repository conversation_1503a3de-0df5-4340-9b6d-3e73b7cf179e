"""
Health check and monitoring endpoints.
"""
import psutil
from datetime import datetime, timezone
from typing import Dict, Any
from fastapi import APIRouter, Request, status, Depends
from fastapi.responses import J<PERSON>NResponse
from loguru import logger

from worker.task_manager import DEFAULT_QUEUE_SIZE as LIMIT_QUEUE_SIZE
from api.middleware.metrics import get_metrics
from api.middleware.error_handler import ServiceUnavailableError
from api.middleware.auth import require_read, get_optional_user
from core.config import get_settings

router = APIRouter()
settings = get_settings()


def get_system_metrics() -> Dict[str, Any]:
    """Get system resource metrics."""
    try:
        return {
            "cpu_percent": psutil.cpu_percent(interval=0.1),
            "memory": {
                "percent": psutil.virtual_memory().percent,
                "available_gb": round(psutil.virtual_memory().available / (1024**3), 2),
                "total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
            },
            "disk": {
                "percent": psutil.disk_usage('/').percent,
                "free_gb": round(psutil.disk_usage('/').free / (1024**3), 2),
                "total_gb": round(psutil.disk_usage('/').total / (1024**3), 2),
            },
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
        }
    except Exception as e:
        logger.warning(f"Failed to get system metrics: {e}")
        return {"error": "Failed to collect system metrics"}


def check_service_health(task_queue) -> Dict[str, Any]:
    """Check the health of various services."""
    health_status = {
        "overall": "healthy",
        "services": {},
        "issues": []
    }

    try:
        # Check task queue
        queue_sizes = task_queue.get_queue_sizes()
        active_tasks = len(task_queue.active_async_tasks)
        max_workers = task_queue.LIMIT_SUBTITLE_WORKERS + task_queue.LIMIT_SUMMARIZE_WORKERS

        queue_health = "healthy"
        if any(size >= LIMIT_QUEUE_SIZE for size in queue_sizes.values()):
            queue_health = "overloaded"
            health_status["issues"].append("One or more queues are at capacity")
        elif active_tasks >= max_workers:
            queue_health = "overloaded"
            health_status["issues"].append("Maximum worker capacity reached")
        elif any(size >= LIMIT_QUEUE_SIZE * 0.8 for size in queue_sizes.values()):
            queue_health = "warning"
            health_status["issues"].append("Queues approaching capacity")

        health_status["services"]["task_queue"] = {
            "status": queue_health,
            "queue_sizes": queue_sizes,
            "active_tasks": active_tasks,
            "max_workers": max_workers,
        }

        # Check system resources
        system_metrics = get_system_metrics()
        if "error" not in system_metrics:
            system_health = "healthy"
            if system_metrics["cpu_percent"] > 90:
                system_health = "warning"
                health_status["issues"].append("High CPU usage")
            if system_metrics["memory"]["percent"] > 90:
                system_health = "warning"
                health_status["issues"].append("High memory usage")
            if system_metrics["disk"]["percent"] > 90:
                system_health = "warning"
                health_status["issues"].append("Low disk space")

            health_status["services"]["system"] = {
                "status": system_health,
                "metrics": system_metrics,
            }

        # Determine overall health
        service_statuses = [service["status"] for service in health_status["services"].values()]
        if "overloaded" in service_statuses:
            health_status["overall"] = "overloaded"
        elif "warning" in service_statuses:
            health_status["overall"] = "warning"

    except Exception as e:
        logger.error(f"Error checking service health: {e}", exc_info=True)
        health_status["overall"] = "error"
        health_status["issues"].append(f"Health check failed: {str(e)}")

    return health_status


@router.post("/ping", status_code=status.HTTP_200_OK)
async def ping(request: Request):
    """
    Simple ping endpoint for basic availability check.

    Returns:
        JSONResponse: Simple pong response or 503 if overloaded
    """
    try:
        task_queue = request.app.state.task_queue
        queue_sizes = task_queue.get_queue_sizes()

        overload = any(size >= LIMIT_QUEUE_SIZE for size in queue_sizes.values())

        if overload:
            raise ServiceUnavailableError("Server queue overloaded")

        return JSONResponse(
            content={"message": "pong", "timestamp": datetime.now(timezone.utc).isoformat()},
            status_code=200
        )

    except ServiceUnavailableError:
        raise
    except Exception as e:
        logger.error(f"Error in /ping endpoint: {str(e)}", exc_info=True)
        return JSONResponse(
            content={"error": "Internal server error"},
            status_code=500
        )


@router.get("/health", status_code=status.HTTP_200_OK)
async def health_check(request: Request):
    """
    Comprehensive health check endpoint.

    Returns:
        JSONResponse: Detailed health information
    """
    try:
        task_queue = request.app.state.task_queue
        health_status = check_service_health(task_queue)

        # Add application info
        health_status["application"] = {
            "name": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "environment": getattr(settings, 'ENVIRONMENT', 'development'),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Determine HTTP status code based on health
        if health_status["overall"] == "overloaded":
            status_code = 503
        elif health_status["overall"] == "error":
            status_code = 500
        elif health_status["overall"] == "warning":
            status_code = 200  # Still operational
        else:
            status_code = 200

        return JSONResponse(content=health_status, status_code=status_code)

    except Exception as e:
        logger.error(f"Error in /health endpoint: {str(e)}", exc_info=True)
        return JSONResponse(
            content={
                "overall": "error",
                "error": "Health check failed",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
            status_code=500
        )


@router.get("/metrics", status_code=status.HTTP_200_OK)
async def get_application_metrics(
    request: Request,
    user: Dict[str, Any] = Depends(require_read)
):
    """
    Get application metrics.

    Requires: read permission

    Returns:
        JSONResponse: Application metrics
    """
    try:
        task_queue = request.app.state.task_queue

        # Get application metrics
        app_metrics = get_metrics()

        # Get queue metrics
        queue_metrics = {
            "queue_sizes": task_queue.get_queue_sizes(),
            "active_tasks": len(task_queue.active_async_tasks),
            "max_workers": {
                "subtitle": task_queue.LIMIT_SUBTITLE_WORKERS,
                "summarize": task_queue.LIMIT_SUMMARIZE_WORKERS,
                "total": task_queue.LIMIT_SUBTITLE_WORKERS + task_queue.LIMIT_SUMMARIZE_WORKERS,
            }
        }

        # Get system metrics
        system_metrics = get_system_metrics()

        metrics = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "application": app_metrics,
            "queues": queue_metrics,
            "system": system_metrics,
        }

        return JSONResponse(content=metrics, status_code=200)

    except Exception as e:
        logger.error(f"Error in /metrics endpoint: {str(e)}", exc_info=True)
        return JSONResponse(
            content={"error": "Failed to collect metrics"},
            status_code=500
        )
