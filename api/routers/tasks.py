"""
Task management endpoints.
"""
from typing import Dict, Any
from fastapi import APIRouter, Request, Depends
from loguru import logger

from models.schemas import SubtitleResponse, SummarizeResponse
from api.middleware.error_handler import NotFoundError
from api.middleware.auth import require_read

router = APIRouter()


@router.get("/task/{task_id}", response_model=SubtitleResponse | SummarizeResponse)
async def get_task(
    task_id: str,
    request: Request,
    user: Dict[str, Any] = Depends(require_read)
):
    """
    Get the status or result of any task (subtitles or summarization).

    Requires: read permission
    """
    task_queue = request.app.state.task_queue

    logger.debug(f"Received request to get task status for task_id: {task_id}")

    task = await task_queue.get_task(task_id)
    if task is None:
        logger.debug(f"Task not found: {task_id}")
        raise NotFoundError(f"Task with ID '{task_id}' not found")

    logger.debug(f"Task found: {task_id}, status: {task.status}")
    return task
