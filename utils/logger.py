import sys
import json
import logging
from pathlib import Path
from typing import Any, Dict, Optional

import structlog
from loguru import logger as loguru_logger


def setup_structured_logging(debug: bool = False, use_json: bool = False) -> structlog.BoundLogger:
    """
    Setup structured logging with both loguru and structlog.

    Args:
        debug: Enable debug logging level
        use_json: Use JSON format for structured logs

    Returns:
        Configured structlog logger
    """
    # Set log level based on debug mode
    log_level = "DEBUG" if debug else "INFO"

    # Configure structlog processors
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
    ]

    if debug:
        # Add more detailed processors for debug mode
        processors.extend([
            structlog.processors.CallsiteParameterAdder(
                parameters=[structlog.processors.CallsiteParameter.FILENAME,
                           structlog.processors.CallsiteParameter.FUNC_NAME,
                           structlog.processors.CallsiteParameter.LINENO]
            ),
        ])

    if use_json:
        # JSON format for production/structured logging
        processors.append(structlog.processors.JSONRenderer())
    else:
        # Human-readable format for development
        processors.append(structlog.dev.ConsoleRenderer(colors=True))

    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            logging.DEBUG if debug else logging.INFO
        ),
        logger_factory=structlog.WriteLoggerFactory(),
        cache_logger_on_first_use=True,
    )

    # Setup loguru for backward compatibility
    setup_loguru_logging(debug=debug)

    return structlog.get_logger()


def setup_loguru_logging(debug: bool = False):
    """
    Setup loguru logging for backward compatibility.

    Args:
        debug: Enable debug logging level
    """
    # Remove default handler
    loguru_logger.remove()

    # Set log level based on debug mode
    log_level = "DEBUG" if debug else "INFO"

    # Add file handler with structured format
    log_file = Path(__file__).parent.parent / "app.log"
    loguru_logger.add(
        log_file,
        rotation="10 MB",
        retention="1 week",
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        backtrace=True,
        diagnose=True,
        serialize=False,  # Keep human-readable format for file logs
    )

    # Add stdout handler with more detailed format for debug mode
    if debug:
        format_string = (
            "<green>{time:HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    else:
        format_string = (
            "<green>{time:HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<level>{message}</level>"
        )

    loguru_logger.add(
        sys.stdout,
        colorize=True,
        level=log_level,
        format=format_string
    )


def setup_logging(debug: bool = False, structured: bool = True) -> Any:
    """
    Setup logging system.

    Args:
        debug: Enable debug logging level
        structured: Use structured logging (structlog) if True, otherwise use loguru

    Returns:
        Configured logger (structlog or loguru)
    """
    if structured:
        return setup_structured_logging(debug=debug, use_json=not debug)
    else:
        setup_loguru_logging(debug=debug)
        return loguru_logger


def get_logger(name: Optional[str] = None) -> structlog.BoundLogger:
    """
    Get a structured logger instance.

    Args:
        name: Logger name (optional)

    Returns:
        Structured logger instance
    """
    if name:
        return structlog.get_logger(name)
    return structlog.get_logger()
