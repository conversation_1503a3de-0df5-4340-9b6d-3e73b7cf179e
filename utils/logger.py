import sys
from pathlib import Path
from loguru import logger


def setup_logging(debug: bool = False):
    # Remove default handler
    logger.remove()

    # Set log level based on debug mode
    log_level = "DEBUG" if debug else "INFO"

    # Add file handler
    log_file = Path(__file__).parent.parent / "app.log"
    logger.add(
        log_file,
        rotation="10 MB",
        retention="1 week",
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
        backtrace=True,
        diagnose=True,
    )

    # Add stdout handler with more detailed format for debug mode
    format_string = (
        (
            "<green>{time:HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        if debug
        else "<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <level>{message}</level>"
    )

    logger.add(sys.stdout, colorize=True,
               level=log_level, format=format_string)

    return logger
