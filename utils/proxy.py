import os
import socks
from loguru import logger
from typing import Optional


class ProxyManager:
    def __init__(self):
        self.proxy_url = os.getenv("SOCKS5_PROXY")
        self._is_connected = False

    async def setup_proxy(self) -> bool:
        if not self.proxy_url:
            logger.info("No SOCKS5 proxy configured, using direct connection")
            return False

        try:
            # Parse proxy URL format: socks5://host:port
            if not self.proxy_url.startswith("socks5://"):
                raise ValueError(
                    "Invalid proxy URL format. Must be socks5://host:port")

            host = self.proxy_url.split("://")[1].split(":")[0]
            port = int(self.proxy_url.split(":")[-1])

            # Set up SOCKS5 proxy
            socks.set_default_proxy(socks.SOCKS5, host, port)
            logger.info(f"SOCKS5 proxy configured: {self.proxy_url}")
            # We don't test connection here, rely on libraries using the proxy
            self._is_connected = True
            return True

        except Exception as e:
            logger.error(f"Failed to configure SOCKS5 proxy: {str(e)}")
            # Reset to direct connection
            socks.set_default_proxy()
            self._is_connected = False
            return False

    @property
    def is_connected(self) -> bool:
        return self._is_connected

    def get_yt_dlp_proxy(self) -> Optional[str]:
        """Returns proxy URL in format suitable for yt-dlp if connected"""
        return self.proxy_url if self._is_connected else None
