import asyncio
import heapq
import time
from typing import Dict, Set, List, Any, <PERSON><PERSON>, Optional, Union
from loguru import logger

from models.schemas import (
    SubtitleResponse,
    SummarizeResponse,
    TaskStatus,
    VideoListResponse,
)
from worker.ws_notifier import WebSocketNotifier
from worker.task_handlers import TaskHandler
from worker.rate_limiter import YouTubeRateLimiter, SummarizeRateLimiter
from worker.task_priority import TaskPriority, PrioritizedTask, get_priority_from_string

# Type aliases
TaskResponse = Union[SubtitleResponse, SummarizeResponse, VideoListResponse]
TaskQueue = asyncio.PriorityQueue[PrioritizedTask]  # type: ignore


# Queue configuration constants
DEFAULT_QUEUE_SIZE = 100
DEFAULT_WORKERS = {
    'subtitle': 2,
    'summarize': 3,
    'video_list': 2
}

# Task timing constants
TASK_TIMEOUT = 120  # seconds
TASK_TYPE_DELAY_MS = 500  # milliseconds

# Rate limiting
DEFAULT_RATE_LIMITS = {
    'youtube': 15,  # requests per minute
    'summarize': 5,  # requests per minute
}

# Task type constants
TASK_TYPES = {
    'subtitle': 'subtitle',
    'summarize': 'summarize',
    'video_list': 'video_list'
}


class TaskManager:
    """Manages task queues and processing with priority support.
    
    This class handles task submission, prioritization, rate limiting, and worker management
    for different types of tasks (subtitle downloads, summarization, etc.).
    """

    def __init__(self, ws_manager: Any):
        # Initialize task queues with priority support
        self.queues: Dict[str, TaskQueue] = {
            task_type: asyncio.PriorityQueue(maxsize=DEFAULT_QUEUE_SIZE)
            for task_type in TASK_TYPES.values()
        }

        # Task storage and status tracking
        self.tasks: Dict[str, TaskResponse] = {}
        self.active_async_tasks: Dict[str, asyncio.Task] = {}
        self.cancelled_tasks: Set[str] = set()
        self.worker_manager_tasks: List[asyncio.Task] = []

        # WebSocket management
        self.ws_manager = ws_manager
        self.ws_notifier = WebSocketNotifier(ws_manager)

        # Task handler
        self.task_handler = TaskHandler(TASK_TIMEOUT, self.ws_notifier)

        # Rate limiters
        self.rate_limiters = {
            'youtube': YouTubeRateLimiter(DEFAULT_RATE_LIMITS['youtube']),
            'summarize': SummarizeRateLimiter(DEFAULT_RATE_LIMITS['summarize'])
        }

        # Worker management
        self.semaphores = {
            task_type: asyncio.Semaphore(workers)
            for task_type, workers in DEFAULT_WORKERS.items()
        }

        # Statistics
        self.stats = {
            'tasks_processed': 0,
            'tasks_failed': 0,
            'tasks_cancelled': 0,
            'queue_lengths': {task_type: 0 for task_type in TASK_TYPES.values()}
        }
        
        # For access from main.py
        self.LIMIT_SUBTITLE_WORKERS = DEFAULT_WORKERS['subtitle']
        self.LIMIT_SUMMARIZE_WORKERS = DEFAULT_WORKERS['summarize']

    async def initialize(self):
        """Инициализация компонентов"""
        logger.debug("TaskManager initializing...")

        # Инициализация обработчика задач
        await self.task_handler.initialize()

        # Start worker managers for each task type
        logger.debug("Starting worker managers...")
        for task_type in TASK_TYPES.values():
            manager_task = asyncio.create_task(self._task_worker_manager(task_type))
            self.worker_manager_tasks.append(manager_task)

        # Настройка WebSocket менеджера
        self.ws_manager.set_task_queue_reference(self)
        await self.ws_manager.start_cleanup_task()

        logger.debug("TaskManager initialized successfully.")

    async def is_task_active(self, task_id: str) -> bool:
        """Check if a task is currently active.
        
        Args:
            task_id: The ID of the task to check
            
        Returns:
            bool: True if the task is active, False otherwise
        """
        async_task = self.active_async_tasks.get(task_id)
        return async_task is not None and not async_task.done()

    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a running task or mark a pending task for cancellation.
        
        Args:
            task_id: The ID of the task to cancel
            
        Returns:
            bool: True if cancellation was successful, False otherwise
        """
        logger.debug(f"Cancellation requested for task_id: {task_id}")
        self.cancelled_tasks.add(task_id)
        
        # Check if task is currently running
        async_task = self.active_async_tasks.get(task_id)
        if async_task and not async_task.done():
            logger.debug(f"Attempting to cancel active asyncio.Task for {task_id}")
            async_task.cancel()
            return True
            
        # If task is in queue, we need to find and remove it
        for queue in self.queues.values():
            # Create a temporary list to hold items while we search
            temp_items = []
            task_found = False
            
            # Process all items in the queue
            while not queue.empty():
                try:
                    item = await queue.get()
                    if item.task_id == task_id:
                        logger.debug(f"Removed task {task_id} from queue")
                        task_found = True
                        self.stats['tasks_cancelled'] += 1
                        # Skip putting this item back in the queue
                        continue
                    temp_items.append(item)
                except asyncio.QueueEmpty:
                    break
            
            # Put items back in the queue (except the cancelled one)
            for item in temp_items:
                await queue.put(item)
                
            if task_found:
                break
                
        # Update task status if it exists
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                task.status = TaskStatus.CANCELLED
                task.error = "Task cancelled by user"
                await self.ws_notifier.notify_subscribers_of_cancellation(
                    task_id, "Task cancelled by user", self.tasks
                )
                return True
                
        return False

    async def _process_task(
        self,
        task_type: str,
        task_id: str,
        task_data: Any,
        priority: TaskPriority = TaskPriority.NORMAL
    ) -> None:
        """Process a single task with proper error handling and resource management.
        
        Args:
            task_type: Type of the task (subtitle, summarize, video_list)
            task_id: Unique identifier for the task
            task_data: Task-specific data
            priority: Priority level of the task
        """
        semaphore = self.semaphores[task_type]
        rate_limiter = self.rate_limiters.get(task_type)
        
        try:
            # Apply rate limiting if needed
            if rate_limiter and not rate_limiter.can_make_request():
                wait_time = rate_limiter.get_wait_time()
                if wait_time > 0:
                    logger.debug(f"Rate limited. Waiting {wait_time:.2f}s for {task_type} task {task_id}")
                    await asyncio.sleep(wait_time)
            
            # Mark task as started
            if task_id in self.tasks:
                self.tasks[task_id].status = TaskStatus.PROCESSING
                await self.ws_notifier.notify_task_update(task_id, self.tasks[task_id])
            
            # Process the task
            if task_type == TASK_TYPES['subtitle']:
                await self.task_handler.process_subtitle_task(task_id, task_data, self.tasks)
            elif task_type == TASK_TYPES['summarize']:
                await self.task_handler.process_summarize_task(task_id, task_data, self.tasks)
            elif task_type == TASK_TYPES['video_list']:
                await self.task_handler.process_video_list_task(task_id, task_data, self.tasks)
            
            # Update stats
            self.stats['tasks_processed'] += 1
            
        except asyncio.CancelledError:
            logger.info(f"{task_type.capitalize()} task {task_id} was cancelled during processing.")
            if task_id in self.tasks:
                self.tasks[task_id].status = TaskStatus.CANCELLED
                self.tasks[task_id].error = "Task cancelled during processing."
                await self.ws_notifier.notify_subscribers_of_cancellation(
                    task_id, "Task cancelled during processing.", self.tasks
                )
            self.stats['tasks_cancelled'] += 1
            
        except Exception as e:
            logger.error(f"Error in {task_type} task {task_id}: {e}", exc_info=True)
            if task_id in self.tasks:
                self.tasks[task_id].status = TaskStatus.FAILED
                self.tasks[task_id].error = str(e)
                await self.ws_notifier.notify_task_update(task_id, self.tasks[task_id])
            self.stats['tasks_failed'] += 1
            
        finally:
            # Release resources
            semaphore.release()
            self.active_async_tasks.pop(task_id, None)
            
            # Apply delay between tasks of the same type
            await asyncio.sleep(TASK_TYPE_DELAY_MS / 1000)
            
            # Update rate limiter
            if rate_limiter:
                rate_limiter.add_request(task_id)

    async def _handle_task_done(self, task: asyncio.Task, task_id: str, queue: Any) -> None:
        """Handle task completion asynchronously.
        
        Args:
            task: The completed task
            task_id: ID of the completed task
            queue: The queue or list the task came from
        """
        try:
            # Get the result to raise any exceptions
            task.result()
        except asyncio.CancelledError:
            logger.debug(f"Task {task_id} was cancelled")
        except Exception as e:
            logger.error(f"Task {task_id} failed: {e}", exc_info=True)
        finally:
            try:
                # Handle both queue and list types
                if hasattr(queue, 'task_done'):  # It's a queue
                    if hasattr(queue, '_queue') and hasattr(queue._queue, 'empty') and not queue._queue.empty():
                        queue.task_done()
                    
                    # Update statistics if queue has a name
                    if hasattr(queue, '_queue_name') and 'queue_lengths' in self.stats:
                        self.stats['queue_lengths'][queue._queue_name] = queue.qsize()
                
                # Release the semaphore if this was a worker task
                if task_id in self.active_async_tasks:
                    # Get the task type from the queue name or by finding the queue in self.queues
                    task_type = None
                    
                    # First try to get task_type from queue name if it exists
                    if hasattr(queue, '_queue_name'):
                        task_type = queue._queue_name
                    
                    # If not found, try to find by queue object
                    if task_type is None:
                        for t, q in self.queues.items():
                            if q == queue:
                                task_type = t
                                break
                    
                    if task_type and task_type in self.semaphores:
                        self.semaphores[task_type].release()
                    
                    # Clean up
                    self.active_async_tasks.pop(task_id, None)
                    
            except Exception as e:
                logger.error(f"Error in task cleanup for {task_id}: {e}", exc_info=True)

    async def _task_worker_manager(self, task_type: str) -> None:
        """Worker manager for processing tasks of a specific type.
        
        Args:
            task_type: Type of tasks to process (subtitle, summarize, video_list)
        """
        logger.info(f"{task_type.capitalize()} worker manager started")
        queue = self.queues[task_type]
        
        while True:
            try:
                # Wait for a new task from the queue
                prioritized_task = await queue.get()
                task_id = prioritized_task.task_id
                task_data = prioritized_task.data
                
                # Check if task was cancelled
                if task_id in self.cancelled_tasks:
                    logger.debug(f"Skipping cancelled {task_type} task {task_id}")
                    self.cancelled_tasks.discard(task_id)
                    queue.task_done()
                    continue
                
                # Get semaphore for concurrency control
                semaphore = self.semaphores[task_type]
                await semaphore.acquire()
                
                # Create and start the task
                task = asyncio.create_task(
                    self._process_task(task_type, task_id, task_data, prioritized_task.priority)
                )
                self.active_async_tasks[task_id] = task
                
                # Add callback for cleanup
                def done_callback(task):
                    # Schedule the async callback to run in the event loop
                    asyncio.create_task(self._handle_task_done(task, task_id, queue))
                
                task.add_done_callback(done_callback)
                
            except asyncio.CancelledError:
                logger.info(f"{task_type.capitalize()} worker manager cancelled")
                break
                
            except Exception as e:
                logger.error(f"Error in {task_type} worker manager: {e}", exc_info=True)
                await asyncio.sleep(1)  # Prevent tight error loop

    async def add_task(self, task_type: str, task_id: str, task_data: Any, priority: str = 'normal') -> bool:
        """Add a new task to the appropriate queue.
        
        Args:
            task_type: Type of task (subtitle, summarize, video_list)
            task_id: Unique identifier for the task
            task_data: Task-specific data
            priority: Priority level (low, normal, high, urgent)
            
        Returns:
            bool: True if task was added successfully, False otherwise
        """
        if task_type not in self.queues:
            logger.error(f"Unknown task type: {task_type}")
            return False
            
        # Convert string priority to enum
        priority_enum = get_priority_from_string(priority)
        
        try:
            # Create a prioritized task
            prioritized_task = PrioritizedTask(
                priority=priority_enum,
                task_id=task_id,
                data=task_data
            )
            
            # Add to the appropriate queue
            await self.queues[task_type].put(prioritized_task)
            logger.debug(f"Added {task_type} task {task_id} with priority {priority_enum.name}")
            
            # Update statistics
            self.stats['queue_lengths'][task_type] = self.queues[task_type].qsize()
            
            return True
        except Exception as e:
            logger.error(f"Error in {task_type} worker manager: {e}", exc_info=True)
            await asyncio.sleep(1)  # Prevent tight error loop

    async def add_task(self, task_type: str, task_id: str, task_data: Any, priority: str = 'normal') -> bool:
        """Add a new task to the appropriate queue.
        
        Args:
            task_type: Type of task (subtitle, summarize, video_list)
            task_id: Unique identifier for the task
            task_data: Task-specific data
            priority: Priority level (low, normal, high, urgent)
            
        Returns:
            bool: True if task was added successfully, False otherwise
        """
        if task_type not in self.queues:
            logger.error(f"Unknown task type: {task_type}")
            return False
            
        # Convert string priority to enum
        priority_enum = get_priority_from_string(priority)
        
        try:
            # Create a prioritized task
            prioritized_task = PrioritizedTask(
                priority=priority_enum,
                task_id=task_id,
                data=task_data
            )
            
            # Add to the appropriate queue
            await self.queues[task_type].put(prioritized_task)
            logger.debug(f"Added {task_type} task {task_id} with priority {priority_enum.name}")
            
            # Update statistics
            self.stats['queue_lengths'][task_type] = self.queues[task_type].qsize()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add task {task_id}: {e}", exc_info=True)
            return False

    async def add_subtitle_task(self, task_id: str, task_data: Tuple, task_obj: SubtitleResponse) -> bool:
        """Add a subtitle task to the queue.
        
        Args:
            task_id: Unique identifier for the task
            task_data: Task-specific data
            task_obj: SubtitleResponse object to track task status
            
        Returns:
            bool: True if task was added successfully, False otherwise
        """
        self.tasks[task_id] = task_obj
        return await self.add_task('subtitle', task_id, task_data, 'normal')

    async def add_summarize_task(self, task_id: str, task_data: Tuple, task_obj: SummarizeResponse) -> bool:
        """Adds a summarization task to the queue.
        
        Args:
            task_id: Unique identifier for the task
            task_data: Task-specific data
            task_obj: SummarizeResponse object to track task status
            
        Returns:
            bool: True if task was added successfully, False otherwise
        """
        self.tasks[task_id] = task_obj
        return await self.add_task('summarize', task_id, task_data, 'normal')

    async def add_video_list_task(self, task_id: str, task_data: Tuple, task_obj: VideoListResponse) -> bool:
        """Adds a video list task to the queue.
        
        Args:
            task_id: Unique identifier for the task
            task_data: Task-specific data
            task_obj: VideoListResponse object to track task status
            
        Returns:
            bool: True if task was added successfully, False otherwise
        """
        self.tasks[task_id] = task_obj
        return await self.add_task('video_list', task_id, task_data, 'normal')

    async def _cleanup_task(self, task_id: str) -> None:
        """Clean up task resources after completion or error.
        
        Args:
            task_id: ID of the task to clean up
        """
        # Cancel and clean up the async task if it exists
        if task_id in self.active_async_tasks:
            task = self.active_async_tasks[task_id]
            if not task.done():
                task.cancel()
                try:
                    await task
                except (asyncio.CancelledError, Exception):
                    pass
            del self.active_async_tasks[task_id]
        
        # Remove from cancelled tasks set if present
        if task_id in self.cancelled_tasks:
            self.cancelled_tasks.remove(task_id)
        
        # Clean up task result if present
        if task_id in self.tasks:
            del self.tasks[task_id]
            
        logger.debug(f"Cleaned up resources for task {task_id}")

    async def add_task(self, task_type: str, task_id: str, task_data: Any, priority: str = 'normal') -> bool:
        """Add a new task to the appropriate queue.
        
        Args:
            task_type: Type of task (subtitle, summarize, video_list)
            task_id: Unique identifier for the task
            task_data: Task-specific data
            priority: Priority level (low, normal, high, urgent)
            
        Returns:
            bool: True if task was added successfully, False otherwise
        """
        if task_type not in self.queues:
            logger.error(f"Unknown task type: {task_type}")
            return False
            
        # Convert string priority to enum
        priority_enum = get_priority_from_string(priority)
        
        try:
            # Create a prioritized task
            prioritized_task = PrioritizedTask(
                priority=priority_enum,
                task_id=task_id,
                data=task_data
            )
            
            # Add to the appropriate queue
            await self.queues[task_type].put(prioritized_task)
            logger.debug(f"Added {task_type} task {task_id} with priority {priority_enum.name}")
            
            # Update statistics
            self.stats['queue_lengths'][task_type] = self.queues[task_type].qsize()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add task {task_id}: {e}", exc_info=True)
            return False

    async def add_subtitle_task(self, task_id: str, task_data: Tuple, task_obj: SubtitleResponse) -> bool:
        """Add a subtitle task to the queue.
        
        Args:
            task_id: Unique identifier for the task
            task_data: Task-specific data
            task_obj: SubtitleResponse object to track task status
            
        Returns:
            bool: True if task was added successfully, False otherwise
        """
        self.tasks[task_id] = task_obj
        return await self.add_task('subtitle', task_id, task_data, 'normal')

    async def add_summarize_task(self, task_id: str, task_data: Tuple, task_obj: SummarizeResponse) -> bool:
        """Adds a summarization task to the queue.
        
        Args:
            task_id: Unique identifier for the task
            task_data: Task-specific data
            task_obj: SummarizeResponse object to track task status
            
        Returns:
            bool: True if task was added successfully, False otherwise
        """
        self.tasks[task_id] = task_obj
        return await self.add_task('summarize', task_id, task_data, 'normal')

    async def add_video_list_task(self, task_id: str, task_data: Tuple, task_obj: VideoListResponse) -> bool:
        """Adds a video list task to the queue.
        
        Args:
            task_id: Unique identifier for the task
            task_data: Task-specific data
            task_obj: VideoListResponse object to track task status
            
        Returns:
            bool: True if task was added successfully, False otherwise
        """
        self.tasks[task_id] = task_obj
        return await self.add_task('video_list', task_id, task_data, 'normal')
