from typing import Dict, Tu<PERSON>, Any

# Базовая конфигурация, общая для всех моделей
BASE_CONFIG = {
    "top_p": 0.95,
    "top_k": 64,
    "max_output_tokens": 65536,
    "response_mime_type": "text/plain",
}

# Конфигурация для каждого режима суммаризации
# Формат: (model_name, temperature, prompt)
MODEL_CONFIGS: Dict[str, Tuple[str, float, str]] = {
    "default": (
        "gemini-2.0-flash-thinking-exp-01-21",
        0.7,
        "Что это. Извлеки суть и ключевые инсайты."
    ),
    "socratic": (
        "gemini-2.5-flash-preview-04-17",
        0.9,
        "Что это? Найди реальную суть и ключевые инсайты. Объясни эту тему в форме сократического диалога - чтобы в результате ученик умел на практике делать то что тут упомянуто, зная и подходы/стратегии и практические современные инструменты. Отвечай на русском языке."
    ),
    "diablo": (
        "gemini-2.5-flash-preview-04-17",
        1.0,
        "Что это? Найди реальную суть и ключевые инсайты. Объясни эту тему в форме диалога по технике 'Адвокат дьявола' - чтобы в результате пользователь критически мыслил и ориентировался в том что тут упомянуто, зная и подходы/стратегии и практические современные инструменты. Отвечай на русском языке."
    ),
    "detailed": (
        "gemini-2.5-flash-preview-04-17",
        1.0,
        """Тебе предоставлен текст. Твоя задача — внимательно изучить его и создать структурированное описание/суммаризацию этого текста. Описание должно быть достаточно подробным, чтобы человек, не знакомый с текстом, мог понять его основное содержание, ключевые концепции и структуру, а также уловить специфику и контекст изложенного материала.

Пожалуйста, организуй свое описание следующим образом:

1.  **Суть текста / Основная идея:**

    - Сформулируй основную **цель** или **посыл** текста (что автор пытается донести, чему научить, какую идею раскрыть, какую проблему осветить).
    - Опиши **подход** или **стиль** автора (например, формальный, неформальный, аналитический, повествовательный, используемые методы изложения, основные акценты).
    - Перечисли основные **темы/разделы**, которые **охватываются** в тексте.
    - Если это подразумевается или явно указано в тексте, отметь, на какую **аудиторию** он ориентирован или какие **предпосылки** (знания, навыки или предыдущий опыт) могут быть полезны для его полного понимания.
    - Используй краткие формулировки или буллит-пойнты для этого раздела.

2.  **Ключевые инсайты / Основные положения:**

    - Извлеки и объясни наиболее важные **идеи, концепции, термины, аргументы, факты или принципы**, которые обсуждаются в тексте.
    - Для каждого ключевого момента дай краткое, но понятное объяснение, основанное на информации из текста, раскрывая его значение в контексте всего материала.
    - Обрати внимание на **специфику контента** и постарайся отразить ее в своем анализе:
      - Если это технический или научный материал, фокусируйся на концепциях, алгоритмах, методологиях, результатах исследований.
      - Если это гуманитарный или философский текст, выделяй основные тезисы, аргументы, исторический или культурный контекст, философские школы, литературные приемы и т.д.
      - Если это новостной или информационный материал, выдели ключевые события, факты, мнения и их значимость.
      - Если это художественный текст, обрати внимание на сюжет, персонажей, темы, символизм, стиль автора.
    - Представь эту информацию в виде последовательных, логически связанных объяснений, возможно, с использованием буллит-пойнтов для подтем, если это уместно.

3.  **Общее впечатление / Заключение по тексту:**
    - Опиши общее впечатление, которое производит текст (например, его убедительность, глубина проработки, актуальность, стиль изложения).
    - Если текст является частью более крупной работы (например, глава книги, статья из серии), укажи это и опиши, как он вписывается в общий контекст, если это возможно на основе предоставленного фрагмента.
    - Отметь, если в тексте есть явные выводы или рекомендации автора.

Постарайся, чтобы твое описание было точным, информативным и структурированным. Адаптируй глубину и специфику анализа в соответствии с характером предоставленного материала. Используй русский язык."""
    )
}


def get_model_config(mode: str) -> Dict[str, Any]:
    """Получить полную конфигурацию для указанного режима."""
    if mode not in MODEL_CONFIGS:
        raise ValueError(f"Неизвестный режим суммаризации: {mode}")

    model_name, temperature, prompt = MODEL_CONFIGS[mode]

    # Объединяем базовую конфигурацию с специфичными параметрами режима
    config = BASE_CONFIG.copy()
    config["temperature"] = temperature

    return {
        "model_name": model_name,
        "config": config,
        "prompt": prompt
    }
