import os
import hashlib
import asyncio
from loguru import logger
from google import genai
from google.genai import types
from dotenv import load_dotenv
from models.database import Database

load_dotenv()
TARGET_API_KEY = os.getenv("GEM_API", "")


class TextSummarizer:
    def __init__(self):
        if not TARGET_API_KEY:
            raise ValueError("GEM_API environment variable is not set")

        self.client = genai.Client(api_key=TARGET_API_KEY)
        self.db = Database()
        logger.info("TextSummarizer initialized with Gemini model and database")

    @staticmethod
    def get_text_hash(text: str) -> str:
        """Generate MD5 hash of the input text"""
        return hashlib.md5(text.strip().encode("utf-8")).hexdigest()

    async def summarize(self, text: str, mode: str = "default") -> str:
        """Summarize the given text using Gemini API or get from cache"""
        try:
            # Generate hash and check cache
            text_hash = self.get_text_hash(text)
            logger.debug(f"Text hash: {text_hash}")

            # Check if we have this summary in database
            original, cached_summary = await self.db.get_summary(text_hash, mode)
            if cached_summary:
                logger.info(
                    f"Found cached summary for hash {text_hash} with mode {mode}"
                )
                return cached_summary

            # If not in cache, generate new summary
            logger.debug(
                f"Starting summarization of text (length: {len(text)}) in {mode} mode"
            )

            from .models_config_new import get_model_config

            model_config = get_model_config(mode)
            logger.debug(
                f"Using model configuration: model_name={model_config['model_name']}, prompt={'None' if model_config['prompt'] is None else 'present'}, config={model_config['config']}"
            )

            # Подготовка контента в зависимости от конфигурации
            if model_config["prompt"] is None:
                # Используем system_instruction из конфигурации
                contents = types.Part.from_text(text=text)
            else:
                # Добавляем инструкцию как дополнительную секцию к тексту
                contents = types.Part.from_text(
                    text=f"""
----------------------

{text}

----------------------

# Задание:
{model_config["prompt"]}
"""
                )

            # Выполняем генерацию контента в отдельном потоке через run_in_executor
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.models.generate_content(
                    model=model_config["model_name"],
                    contents=contents,
                    config=model_config["config"],
                ),
            )

            summary = response.text

            # Save to database
            await self.db.save_summary(text_hash, text.strip(), summary, mode)

            logger.debug(
                f"Summarization completed, result length: {len(summary)}")
            return summary

        except Exception as e:
            error_message = str(e)
            logger.error(f"Summarization error: {error_message}")
            # Создаем специальное исключение для ошибок API
            if "502 Bad Gateway" in error_message or "Server Error" in error_message:
                raise RuntimeError(f"API Service Unavailable: {error_message}")
            raise RuntimeError(f"Summarization Failed: {error_message}")
