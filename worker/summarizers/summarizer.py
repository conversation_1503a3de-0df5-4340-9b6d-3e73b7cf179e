import os
import hashlib
from loguru import logger
import google.generativeai as genai
from dotenv import load_dotenv
from models.database import Database

load_dotenv()
TARGET_API_KEY = os.getenv("GEM_API", "")


class TextSummarizer:
    def __init__(self):
        if not TARGET_API_KEY:
            raise ValueError("GEM_API environment variable is not set")

        genai.configure(api_key=TARGET_API_KEY)
        self.db = Database()
        logger.info("TextSummarizer initialized with Gemini model and database")

    @staticmethod
    def get_text_hash(text: str) -> str:
        """Generate MD5 hash of the input text"""
        return hashlib.md5(text.strip().encode('utf-8')).hexdigest()

    async def summarize(self, text: str, mode: str = "default") -> str:
        """Summarize the given text using Gemini API or get from cache"""
        try:
            # Generate hash and check cache
            text_hash = self.get_text_hash(text)
            logger.debug(f"Text hash: {text_hash}")

            # Check if we have this summary in database
            original, cached_summary = await self.db.get_summary(text_hash, mode)
            if cached_summary:
                logger.info(
                    f"Found cached summary for hash {text_hash} with mode {mode}")
                return cached_summary

            # If not in cache, generate new summary
            logger.debug(
                f"Starting summarization of text (length: {len(text)}) in {mode} mode")

            from .models_config import get_model_config
            model_config = get_model_config(mode)
            model = genai.GenerativeModel(
                model_name=model_config["model_name"],
                generation_config=model_config["config"]
            )
            prompt = model_config["prompt"]

            chat_session = model.start_chat(history=[{
                "role": "user",
                "parts": [text],
            }])

            response = chat_session.send_message(prompt)
            summary = response.text

            # Save to database
            await self.db.save_summary(text_hash, text.strip(), summary, mode)

            logger.debug(
                f"Summarization completed, result length: {len(summary)}")
            return summary

        except Exception as e:
            logger.error(f"Summarization error: {str(e)}")
            raise
