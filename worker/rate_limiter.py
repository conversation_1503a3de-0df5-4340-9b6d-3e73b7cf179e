import time
from typing import List, Dict, Optional
from loguru import logger


class RateLimiter:
    """Класс для управления ограничениями скорости запросов"""

    def __init__(self, limit_per_minute: int = 5, window_size: int = 60):
        """Инициализация ограничителя скорости

        Args:
            limit_per_minute: Максимальное количество запросов в минуту
            window_size: Размер окна в секундах для отслеживания запросов
        """
        self.limit_per_minute = limit_per_minute
        self.window_size = window_size  # в секундах
        self.timestamps: List[float] = []
        self.task_timestamps: Dict[str, float] = {}

    def add_request(self, task_id: Optional[str] = None) -> None:
        """Добавляет запрос в список и сохраняет временную метку

        Args:
            task_id: Идентификатор задачи (опционально)
        """
        current_time = time.time()
        self.timestamps.append(current_time)

        # Очистка старых временных меток
        self._cleanup_timestamps()

        # Если указан task_id, сохраняем его временную метку
        if task_id:
            self.task_timestamps[task_id] = current_time

        logger.debug(
            f"Added request to rate limiter. Current count: {len(self.timestamps)}")

    def _cleanup_timestamps(self) -> None:
        """Очищает старые временные метки, которые вышли за пределы окна"""
        current_time = time.time()
        cutoff_time = current_time - self.window_size

        # Удаляем временные метки старше окна
        self.timestamps = [ts for ts in self.timestamps if ts > cutoff_time]

        # Очищаем старые задачи
        for task_id, timestamp in list(self.task_timestamps.items()):
            if timestamp < cutoff_time:
                del self.task_timestamps[task_id]

    def can_make_request(self) -> bool:
        """Проверяет, можно ли выполнить запрос в соответствии с ограничениями

        Returns:
            True, если запрос можно выполнить, иначе False
        """
        self._cleanup_timestamps()
        return len(self.timestamps) < self.limit_per_minute

    def get_wait_time(self) -> float:
        """Возвращает время ожидания до следующего возможного запроса

        Returns:
            Время в секундах до следующего возможного запроса
        """
        if self.can_make_request():
            return 0.0

        self._cleanup_timestamps()
        if not self.timestamps:
            return 0.0

        # Находим самую старую временную метку
        oldest_timestamp = min(self.timestamps)

        # Вычисляем, когда она выйдет за пределы окна
        return oldest_timestamp + self.window_size - time.time()

    def remove_task(self, task_id: str) -> None:
        """Удаляет задачу из отслеживаемых

        Args:
            task_id: Идентификатор задачи
        """
        if task_id in self.task_timestamps:
            del self.task_timestamps[task_id]
            logger.debug(f"Removed task {task_id} from rate limiter")


class YouTubeRateLimiter(RateLimiter):
    """Ограничитель скорости для запросов к YouTube API"""

    def __init__(self, limit_per_minute: int = 15):
        super().__init__(limit_per_minute=limit_per_minute, window_size=60)


class SummarizeRateLimiter(RateLimiter):
    """Ограничитель скорости для запросов к API суммаризации"""

    def __init__(self, limit_per_minute: int = 5):
        super().__init__(limit_per_minute=limit_per_minute, window_size=60)
