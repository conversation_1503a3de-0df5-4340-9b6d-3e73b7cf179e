# API Configuration
HOST=0.0.0.0
PORT=8000

# Proxy Configuration (optional)
# Format: socks5://host:port
SOCKS5_PROXY=

# yt-dlp Cookies File (optional)
# Path to a Netscape-format cookies file
# COOKIES_FILE=/path/to/your/cookies.txt
COOKIES_FILE=
# Gemini API Configuration
GEM_API=your_gemini_api_key_here

DATABASE_TYPE=sqlite
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=yt_subs

# Logging Configuration
LOG_LEVEL=INFO
STRUCTURED_LOGGING=true
LOG_JSON_FORMAT=false
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false