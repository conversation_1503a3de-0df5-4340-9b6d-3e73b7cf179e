"""
Main application entry point.
This file now only contains the FastAPI app initialization and includes routers.
"""
import argparse
from dotenv import load_dotenv

from core.app import create_app
from core.config import get_settings
from utils.logger import setup_logging

# Parse command line arguments
parser = argparse.ArgumentParser()
parser.add_argument("--debug", action="store_true",
                    help="Enable debug logging")
parser.add_argument("--json-logs", action="store_true",
                    help="Use JSON format for logs")
args = parser.parse_args()

# Load environment variables
load_dotenv()

# Get settings
settings = get_settings()

# Setup logging with debug mode if specified
logger = setup_logging(
    debug=args.debug,
    structured=settings.STRUCTURED_LOGGING
)

# Create FastAPI application
app = create_app()


if __name__ == "__main__":
    import uvicorn
    import os

    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    uvicorn.run(app, host=host, port=port)
