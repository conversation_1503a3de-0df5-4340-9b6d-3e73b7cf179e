# Структурированное логирование

## Обзор

В рамках Фазы 2 улучшений проекта была реализована система структурированного логирования с использованием `structlog`. Это обеспечивает:

- **Корреляция запросов** - каждый запрос получает уникальный correlation ID
- **Структурированные логи** - логи в JSON формате для production
- **Контекстная информация** - автоматическое добавление пользователя, времени выполнения
- **Фильтрация чувствительных данных** - автоматическое скрытие токенов и паролей

## Конфигурация

### Переменные окружения

```bash
# Основные настройки логирования
LOG_LEVEL=INFO                    # DEBUG, INFO, WARNING, ERROR, CRITICAL
STRUCTURED_LOGGING=true           # Включить структурированное логирование
LOG_JSON_FORMAT=false             # JSON формат (true для production)

# Детальное логирование (осторожно с чувствительными данными)
LOG_REQUEST_BODY=false            # Логировать тела запросов
LOG_RESPONSE_BODY=false           # Логировать тела ответов
```

### Запуск с различными режимами логирования

```bash
# Режим разработки (человекочитаемые логи)
python main.py --debug

# Production режим (JSON логи)
LOG_JSON_FORMAT=true python main.py

# Отключение структурированного логирования
STRUCTURED_LOGGING=false python main.py
```

## Структура логов

### Логи запросов

```json
{
  "timestamp": "2024-01-15T10:30:45.123456Z",
  "level": "info",
  "event": "request_started",
  "correlation_id": "550e8400-e29b-41d4-a716-446655440000",
  "user_id": "user_123",
  "user_name": "demo_user",
  "user_permissions": ["read", "write"],
  "method": "POST",
  "url": "http://localhost:8000/api/subtitles",
  "path": "/api/subtitles",
  "query_params": {},
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0...",
  "headers": {
    "authorization": "[REDACTED]",
    "content-type": "application/json"
  }
}
```

### Логи ответов

```json
{
  "timestamp": "2024-01-15T10:30:47.456789Z",
  "level": "info",
  "event": "request_completed",
  "correlation_id": "550e8400-e29b-41d4-a716-446655440000",
  "status_code": 200,
  "process_time": 2.3334,
  "response_headers": {
    "content-type": "application/json"
  }
}
```

### Логи ошибок

```json
{
  "timestamp": "2024-01-15T10:30:50.789012Z",
  "level": "error",
  "event": "task_error",
  "correlation_id": "550e8400-e29b-41d4-a716-446655440000",
  "task_id": "task_456",
  "error_message": "Failed to download subtitles",
  "error_type": "external_service_error",
  "retryable": true,
  "error_details": {
    "service": "youtube",
    "status_code": 503
  },
  "original_exception": "HTTPError: 503 Service Unavailable",
  "original_exception_type": "HTTPError"
}
```

## Использование в коде

### Получение логгера

```python
import structlog

# Получить логгер для модуля
logger = structlog.get_logger(__name__)

# Или использовать утилиту
from utils.logger import get_logger
logger = get_logger(__name__)
```

### Структурированное логирование

```python
# Простое логирование
logger.info("User logged in", user_id="123", action="login")

# Логирование с контекстом
logger.error(
    "Database connection failed",
    database="postgres",
    host="localhost",
    port=5432,
    error_code="connection_timeout"
)

# Логирование с привязкой контекста
bound_logger = logger.bind(user_id="123", session_id="abc")
bound_logger.info("Action performed", action="create_task")
bound_logger.info("Another action", action="update_task")
```

### Использование correlation ID

```python
import structlog

# В middleware correlation ID автоматически добавляется
# В обработчиках можно получить текущий контекст
with structlog.contextvars.bind_contextvars(
    operation="subtitle_extraction",
    video_id="abc123"
):
    logger.info("Starting subtitle extraction")
    # Все логи внутри этого блока будут содержать operation и video_id
    
    try:
        result = extract_subtitles(video_id)
        logger.info("Subtitle extraction completed", subtitle_count=len(result))
    except Exception as e:
        logger.error("Subtitle extraction failed", error=str(e))
```

## Фильтрация чувствительных данных

Система автоматически скрывает чувствительную информацию:

### Заголовки HTTP

```python
# Автоматически скрываются:
sensitive_headers = {
    "authorization",
    "cookie", 
    "x-api-key",
    "x-auth-token",
    "x-csrf-token",
    "x-session-id",
    "set-cookie"
}
```

### Пример фильтрации

```json
{
  "headers": {
    "authorization": "[REDACTED]",
    "x-api-key": "[REDACTED]",
    "content-type": "application/json",
    "user-agent": "curl/7.68.0"
  }
}
```

## Мониторинг и анализ

### Поиск по correlation ID

```bash
# Найти все логи для конкретного запроса
grep "550e8400-e29b-41d4-a716-446655440000" app.log

# Или в JSON логах
jq '.correlation_id == "550e8400-e29b-41d4-a716-446655440000"' app.log
```

### Анализ производительности

```bash
# Найти медленные запросы (> 5 секунд)
jq 'select(.process_time > 5)' app.log

# Статистика по эндпойнтам
jq -r '.path' app.log | sort | uniq -c | sort -nr
```

### Анализ ошибок

```bash
# Все ошибки за последний час
jq 'select(.level == "error" and (.timestamp | fromdateiso8601) > (now - 3600))' app.log

# Группировка ошибок по типу
jq -r 'select(.level == "error") | .error_type' app.log | sort | uniq -c
```

## Интеграция с внешними системами

### ELK Stack

```yaml
# logstash.conf
input {
  file {
    path => "/path/to/app.log"
    codec => "json"
  }
}

filter {
  if [correlation_id] {
    mutate {
      add_field => { "[@metadata][correlation_id]" => "%{correlation_id}" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "yt-subs-api-%{+YYYY.MM.dd}"
  }
}
```

### Grafana Dashboard

```json
{
  "dashboard": {
    "title": "YT Subs API Logs",
    "panels": [
      {
        "title": "Request Rate",
        "targets": [
          {
            "expr": "rate(log_entries{event=\"request_started\"}[5m])"
          }
        ]
      },
      {
        "title": "Error Rate", 
        "targets": [
          {
            "expr": "rate(log_entries{level=\"error\"}[5m])"
          }
        ]
      }
    ]
  }
}
```

## Лучшие практики

### 1. Используйте структурированные поля

```python
# ✅ Хорошо
logger.info("User action", user_id="123", action="login", ip="*******")

# ❌ Плохо  
logger.info(f"User 123 logged in from *******")
```

### 2. Добавляйте контекст

```python
# ✅ Хорошо
logger.error("Database error", table="users", operation="insert", error_code="23505")

# ❌ Плохо
logger.error("Error occurred")
```

### 3. Используйте правильные уровни

```python
# DEBUG - детальная отладочная информация
logger.debug("Processing item", item_id="123", step="validation")

# INFO - важные события
logger.info("Task completed", task_id="456", duration=2.5)

# WARNING - потенциальные проблемы
logger.warning("Rate limit approaching", current_rate=90, limit=100)

# ERROR - ошибки, требующие внимания
logger.error("External service failed", service="youtube", status=503)

# CRITICAL - критические ошибки
logger.critical("Database connection lost", database="postgres")
```

### 4. Не логируйте чувствительные данные

```python
# ✅ Хорошо
logger.info("User authenticated", user_id="123")

# ❌ Плохо
logger.info("User authenticated", password="secret123")
```

## Миграция с loguru

Существующий код с loguru продолжает работать благодаря обратной совместимости:

```python
# Старый код продолжает работать
from loguru import logger
logger.info("This still works")

# Новый код использует structlog
import structlog
logger = structlog.get_logger(__name__)
logger.info("New structured logging", key="value")
```

## Производительность

- **Overhead**: ~5-10% дополнительного времени на запрос
- **Memory**: ~1-2MB дополнительной памяти для буферизации
- **Disk**: JSON логи на ~20-30% больше текстовых

## Troubleshooting

### Проблема: Логи не появляются

```python
# Проверить конфигурацию
import structlog
print(structlog.is_configured())

# Проверить уровень логирования
import logging
print(logging.getLogger().level)
```

### Проблема: Correlation ID не добавляется

```python
# Убедиться, что middleware добавлен
# В core/app.py должно быть:
app.add_middleware(StructuredLoggingMiddleware)
```

### Проблема: JSON логи нечитаемы

```bash
# Использовать jq для форматирования
tail -f app.log | jq '.'

# Или включить человекочитаемый формат
LOG_JSON_FORMAT=false python main.py
```
