# Улучшенная валидация данных

## Обзор

В рамках Фазы 3 улучшений проекта была реализована комплексная система валидации данных с использованием Pydantic. Это обеспечивает:

- **Строгая валидация входных данных** - проверка всех полей на соответствие требованиям
- **Безопасность** - защита от XSS, инъекций и других атак
- **Информативные сообщения об ошибках** - детальные описания проблем валидации
- **Переиспользуемые валидаторы** - единообразная валидация по всему приложению

## Основные компоненты

### 1. Модуль валидаторов (`models/validators.py`)

Содержит переиспользуемые функции валидации:

```python
from models.validators import (
    validate_youtube_url,
    validate_client_uid,
    validate_text_content,
    validate_language_code,
    validate_filename
)
```

### 2. Улучшенные схемы (`models/schemas.py`)

Обновленные Pydantic модели с валидацией:

```python
class SubtitleRequest(BaseModel):
    url: str = youtube_url_field()
    client_uid: Optional[str] = client_uid_field()
    
    @validator('url')
    def validate_url(cls, v):
        return validate_youtube_url(v)
```

### 3. Схемы аутентификации (`models/auth_schemas.py`)

Модели для API ключей с валидацией:

```python
class APIKeyRequest(BaseModel):
    name: str = Field(..., min_length=3, max_length=100)
    permissions: List[Permission] = Field(..., min_items=1)
```

### 4. Схемы файлов (`models/file_schemas.py`)

Валидация загружаемых файлов:

```python
class FileUploadRequest(BaseModel):
    filename: str
    content: str  # base64
    mime_type: Optional[str]
```

## Типы валидации

### YouTube URL валидация

```python
# Поддерживаемые форматы:
"https://www.youtube.com/watch?v=dQw4w9WgXcQ"
"https://youtu.be/dQw4w9WgXcQ"
"https://m.youtube.com/watch?v=dQw4w9WgXcQ"
"https://youtube-nocookie.com/embed/dQw4w9WgXcQ"

# Проверки:
- Валидный домен YouTube
- Наличие video ID
- Формат video ID (11 символов)
```

### Client UID валидация

```python
# Требования:
- Длина: 3-100 символов
- Символы: буквы, цифры, _, -
- Паттерн: ^[a-zA-Z0-9_-]+$

# Примеры валидных UID:
"user_123"
"app-mobile-v2"
"test_user_001"
```

### Текстовая валидация

```python
# Ограничения:
- Минимум: 10 символов
- Максимум: 1,000,000 символов (настраивается)
- Проверка на XSS: <script>, javascript:, data:base64
- Удаление лишних пробелов
```

### Файловая валидация

```python
# Поддерживаемые типы:
ALLOWED_EXTENSIONS = {'.txt', '.md', '.rtf', '.doc', '.docx', '.pdf'}
ALLOWED_MIME_TYPES = {
    'text/plain', 'text/markdown', 'text/rtf',
    'application/msword', 'application/pdf'
}

# Ограничения:
- Максимальный размер: 10MB
- Проверка имени файла на безопасность
- Валидация base64 содержимого
```

## Примеры использования

### Валидация YouTube URL

```python
from models.validators import validate_youtube_url

try:
    url = validate_youtube_url("https://youtu.be/dQw4w9WgXcQ")
    print(f"Valid URL: {url}")
except ValueError as e:
    print(f"Invalid URL: {e}")
```

### Создание запроса с валидацией

```python
from models.schemas import SubtitleRequest

# Валидный запрос
request = SubtitleRequest(
    url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    client_uid="user_123"
)

# Невалидный запрос вызовет ValidationError
try:
    invalid_request = SubtitleRequest(
        url="https://example.com/video",  # Не YouTube URL
        client_uid="user@123"  # Недопустимые символы
    )
except ValidationError as e:
    print(f"Validation errors: {e.errors()}")
```

### Валидация файлов

```python
from models.file_schemas import FileUploadRequest
import base64

# Подготовка файла
with open("document.txt", "rb") as f:
    content = base64.b64encode(f.read()).decode()

# Создание запроса с валидацией
file_request = FileUploadRequest(
    filename="document.txt",
    content=content,
    mime_type="text/plain"
)
```

## Обработка ошибок валидации

### Структура ошибок

```python
from pydantic import ValidationError

try:
    request = SubtitleRequest(url="invalid-url")
except ValidationError as e:
    for error in e.errors():
        print(f"Field: {error['loc']}")
        print(f"Error: {error['type']}")
        print(f"Message: {error['msg']}")
        print(f"Input: {error['input']}")
```

### Пример ошибки

```json
{
  "detail": [
    {
      "loc": ["url"],
      "msg": "URL must be from a YouTube domain. Supported domains: youtube.com, youtu.be, youtube-nocookie.com",
      "type": "value_error",
      "input": "https://example.com/video"
    },
    {
      "loc": ["client_uid"],
      "msg": "Client UID can only contain letters, numbers, underscores, and hyphens",
      "type": "value_error",
      "input": "user@123"
    }
  ]
}
```

## Константы валидации

```python
from models.validators import ValidationConstants

# YouTube домены
ValidationConstants.YOUTUBE_DOMAINS

# Ограничения Client UID
ValidationConstants.CLIENT_UID_MAX_LENGTH  # 100
ValidationConstants.CLIENT_UID_MIN_LENGTH  # 3
ValidationConstants.CLIENT_UID_PATTERN     # ^[a-zA-Z0-9_-]+$

# Ограничения текста
ValidationConstants.MAX_TEXT_LENGTH        # 1,000,000
ValidationConstants.MIN_TEXT_LENGTH        # 10

# Ограничения файлов
ValidationConstants.MAX_FILE_SIZE          # 10MB
ValidationConstants.ALLOWED_FILE_EXTENSIONS
ValidationConstants.ALLOWED_MIME_TYPES

# Поддерживаемые языки
ValidationConstants.SUPPORTED_LANGUAGE_CODES
```

## Кастомные валидаторы

### Создание нового валидатора

```python
def validate_custom_field(value: str) -> str:
    """Validate custom field format."""
    if not value:
        raise ValueError("Field cannot be empty")
    
    if len(value) > 50:
        raise ValueError("Field too long")
    
    if not re.match(r'^[A-Z][a-z]+$', value):
        raise ValueError("Field must start with uppercase letter")
    
    return value

# Использование в модели
class CustomModel(BaseModel):
    custom_field: str
    
    @validator('custom_field')
    def validate_custom(cls, v):
        return validate_custom_field(v)
```

### Фабрики полей

```python
def custom_field(**kwargs):
    """Create a Pydantic field for custom validation."""
    return Field(
        ...,
        min_length=1,
        max_length=50,
        pattern=r'^[A-Z][a-z]+$',
        description="Custom field with specific format",
        **kwargs
    )

# Использование
class Model(BaseModel):
    field: str = custom_field()
```

## Безопасность

### Защита от XSS

```python
# Автоматическая проверка на подозрительные паттерны:
suspicious_patterns = [
    r'<script[^>]*>.*?</script>',  # Script tags
    r'javascript:',               # JavaScript URLs
    r'data:.*base64',            # Base64 data URLs
]
```

### Защита от Path Traversal

```python
# Проверка имен файлов:
if '..' in filename or '/' in filename or '\\' in filename:
    raise ValueError("Filename cannot contain path separators")
```

### Ограничения размеров

```python
# Предотвращение DoS атак:
- Максимальная длина текста: 1MB
- Максимальный размер файла: 10MB
- Максимальное количество элементов в списках: 1000
```

## Производительность

### Оптимизация валидации

```python
# Кэширование скомпилированных регулярных выражений
import re
from functools import lru_cache

@lru_cache(maxsize=128)
def get_compiled_pattern(pattern: str):
    return re.compile(pattern)

# Ранний выход из валидации
def validate_text_fast(text: str) -> str:
    if not text:  # Быстрая проверка
        raise ValueError("Text cannot be empty")
    
    if len(text) > MAX_LENGTH:  # Проверка длины перед regex
        raise ValueError("Text too long")
    
    # Дорогие проверки только если необходимо
    return validate_text_content(text)
```

### Метрики валидации

```python
# Добавление метрик для мониторинга
from prometheus_client import Counter, Histogram

validation_errors = Counter(
    'validation_errors_total',
    'Total validation errors',
    ['field', 'error_type']
)

validation_time = Histogram(
    'validation_duration_seconds',
    'Time spent on validation'
)
```

## Тестирование

### Unit тесты валидаторов

```python
import pytest
from models.validators import validate_youtube_url

def test_valid_youtube_urls():
    valid_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtu.be/dQw4w9WgXcQ",
        "https://m.youtube.com/watch?v=dQw4w9WgXcQ"
    ]
    
    for url in valid_urls:
        assert validate_youtube_url(url) == url

def test_invalid_youtube_urls():
    invalid_urls = [
        "https://example.com/video",
        "not-a-url",
        "https://youtube.com/watch?v=invalid"
    ]
    
    for url in invalid_urls:
        with pytest.raises(ValueError):
            validate_youtube_url(url)
```

### Integration тесты

```python
from fastapi.testclient import TestClient

def test_subtitle_request_validation(client: TestClient):
    # Валидный запрос
    response = client.post("/api/subtitles", json={
        "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "client_uid": "test_user"
    })
    assert response.status_code == 202
    
    # Невалидный запрос
    response = client.post("/api/subtitles", json={
        "url": "invalid-url",
        "client_uid": "user@invalid"
    })
    assert response.status_code == 422
    assert "validation" in response.json()["detail"][0]["type"]
```

## Миграция

### Обновление существующих моделей

1. **Добавить импорты валидаторов**
2. **Обновить типы полей**
3. **Добавить валидаторы**
4. **Обновить тесты**

### Обратная совместимость

- Новые валидации применяются только к новым запросам
- Существующие данные в БД не затрагиваются
- Постепенное внедрение валидации по эндпойнтам
