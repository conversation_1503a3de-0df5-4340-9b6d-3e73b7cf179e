"""Application configuration settings."""

import os
from typing import Optional, Dict, Any
from pydantic import Field, validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # Application settings
    APP_NAME: str = "YouTube Subtitles and Text Summarization API"
    APP_VERSION: str = "0.1.0"
    DEBUG: bool = False

    # Server settings
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = os.getenv("PORT", 8000)
    RELOAD: bool = False

    # API settings
    API_PREFIX: str = "/api"
    DOCS_URL: str = "/docs"
    REDOC_URL: str = "/redoc"
    OPENAPI_URL: str = "/openapi.json"

    # Task queue settings
    MAX_SUBTITLE_WORKERS: int = 1
    MAX_SUMMARIZE_WORKERS: int = 2
    SUBTITLE_QUEUE_SIZE: int = 10
    SUMMARIZE_QUEUE_SIZE: int = 10

    # WebSocket settings
    WEBSOCKET_PING_INTERVAL: int = 30  # seconds
    WEBSOCKET_PING_TIMEOUT: int = 5    # seconds

    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Structured logging settings
    STRUCTURED_LOGGING: bool = True  # Use structlog for structured logging
    LOG_JSON_FORMAT: bool = False    # Use JSON format (True for production)
    LOG_REQUEST_BODY: bool = False   # Log request bodies (be careful with sensitive data)
    LOG_RESPONSE_BODY: bool = False  # Log response bodies (be careful with large responses)
    LOG_EXCLUDE_PATHS: list = ["/health", "/metrics", "/docs", "/redoc", "/openapi.json"]

    # YouTube API settings (if any)
    YOUTUBE_API_KEY: Optional[str] = None

    # Proxy settings
    SOCKS5_PROXY: Optional[str] = None

    # Cookies file for YouTube
    COOKIES_FILE: Optional[str] = None

    # Gemini AI API key
    GEM_API: Optional[str] = None

    # Database settings
    DATABASE_TYPE: str = "sqlite"
    POSTGRES_HOST: Optional[str] = None
    POSTGRES_PORT: Optional[int] = None
    POSTGRES_USER: Optional[str] = None
    POSTGRES_PASSWORD: Optional[str] = None
    POSTGRES_DB: Optional[str] = None

    # Authentication settings
    REQUIRE_AUTH: bool = True  # Set to False to disable authentication
    API_KEYS: Optional[str] = None  # JSON string of API keys for production

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields instead of raising validation errors

    @validator("LOG_LEVEL")
    def validate_log_level(cls, v: str) -> str:
        """Validate that LOG_LEVEL is a valid logging level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()

# Create settings instance
settings = Settings()

def get_settings() -> Settings:
    """Get the application settings."""
    return settings
