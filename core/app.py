"""Main FastAPI application factory."""

from fastapi import FastAPI, status
from fastapi.middleware.cors import CORSMiddleware

from .config import get_settings
from .events import lifespan

settings = get_settings()

def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: The configured FastAPI application instance.
    """
    # Initialize FastAPI application with lifespan events
    app = FastAPI(
        title=settings.APP_NAME,
        description="API for downloading YouTube subtitles and summarizing text using AI",
        version=settings.APP_VERSION,
        docs_url=settings.DOCS_URL,
        redoc_url=settings.REDOC_URL,
        openapi_url=settings.OPENAPI_URL,
        lifespan=lifespan,
    )

    # Configure CORS
    configure_cors(app)

    # Add middleware
    configure_middleware(app)

    # Configure error handlers
    configure_error_handlers(app)

    # Include API routers
    configure_routers(app)

    # Add WebSocket routes
    configure_websocket_routes(app)

    # Add health check endpoint
    add_health_check(app)

    return app

def configure_cors(app: FastAPI) -> None:
    """Configure CORS middleware."""
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # In production, replace with specific origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

def configure_middleware(app: FastAPI) -> None:
    """Configure application middleware."""
    from api.middleware.metrics import metrics_middleware
    from api.middleware.auth import auth_middleware

    # Add metrics collection middleware
    app.middleware("http")(metrics_middleware)

    # Add authentication middleware
    app.middleware("http")(auth_middleware)

def configure_routers(app: FastAPI) -> None:
    """Configure API routers."""
    # Import routers here to avoid circular imports
    from api.routers import subtitles, summarize, tasks, health, auth

    # Include API routers
    app.include_router(health.router, tags=["health"])
    app.include_router(auth.router, prefix="/api", tags=["authentication"])
    app.include_router(subtitles.router, prefix="/api", tags=["subtitles"])
    app.include_router(summarize.router, prefix="/api", tags=["summarize"])
    app.include_router(tasks.router, prefix="/api", tags=["tasks"])

def configure_websocket_routes(app: FastAPI) -> None:
    """Configure WebSocket routes."""
    # Import WebSocket routers here to avoid circular imports
    from api.websockets import subtitles_ws, summarize_ws

    # Include WebSocket routers
    app.include_router(subtitles_ws.router)
    app.include_router(summarize_ws.router)

def configure_error_handlers(app: FastAPI) -> None:
    """Configure error handlers."""
    from fastapi import HTTPException
    from fastapi.exceptions import RequestValidationError
    from api.middleware.error_handler import (
        APIError,
        api_error_handler,
        http_exception_handler,
        validation_exception_handler,
        general_exception_handler,
    )

    # Add custom error handlers
    app.add_exception_handler(APIError, api_error_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)


def add_health_check(app: FastAPI) -> None:
    """Add health check endpoint."""
    # Health check is now handled by the health router
    pass
